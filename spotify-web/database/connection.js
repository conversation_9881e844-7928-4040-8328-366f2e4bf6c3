import { MongoClient } from "mongodb"
import dotenv from "dotenv"

dotenv.config()

let db = null
let client = null

export async function connectToDatabase() {
  if (db) return db

  try {
    client = new MongoClient(process.env.MONGODB_URI)
    await client.connect()
    db = client.db()
    console.log("✅ Connected to MongoDB")
    return db
  } catch (error) {
    console.error("❌ MongoDB connection error:", error)
    throw error
  }
}

export async function getCollection(collectionName) {
  const database = await connectToDatabase()
  return database.collection(collectionName)
}

export async function closeConnection() {
  if (client) {
    await client.close()
    console.log("🔌 MongoDB connection closed")
  }
}

// User token operations
export async function saveUserTokens(discordUserId, tokenData) {
  const collection = await getCollection("spotify_tokens")

  const userData = {
    discord_user_id: discordUserId,
    access_token: tokenData.access_token,
    refresh_token: tokenData.refresh_token,
    expires_at: Date.now() + tokenData.expires_in * 1000,
    updated_at: new Date(),
    created_at: new Date(),
  }

  await collection.replaceOne({ discord_user_id: discordUserId }, userData, { upsert: true })

  console.log(`💾 Saved tokens for Discord user: ${discordUserId}`)
  return userData
}

export async function getUserTokens(discordUserId) {
  const collection = await getCollection("spotify_tokens")
  return await collection.findOne({ discord_user_id: discordUserId })
}

export async function updateAccessToken(discordUserId, newAccessToken, expiresIn) {
  const collection = await getCollection("spotify_tokens")

  await collection.updateOne(
    { discord_user_id: discordUserId },
    {
      $set: {
        access_token: newAccessToken,
        expires_at: Date.now() + expiresIn * 1000,
        updated_at: new Date(),
      },
    },
  )

  console.log(`🔄 Updated access token for Discord user: ${discordUserId}`)
}
