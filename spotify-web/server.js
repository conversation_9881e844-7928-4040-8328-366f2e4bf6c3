const express = require('express');
const path = require('path');
const { MongoClient } = require('mongodb');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// MongoDB connection
let db = null;

async function connectDB() {
    try {
        const client = new MongoClient(process.env.MONGODB_CONNECTION);
        await client.connect();
        db = client.db();
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
    }
}

// Middleware
app.use(express.static(__dirname));
app.use(express.json());

// Routes

// Home page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Login page with Discord ID
app.get('/login', (req, res) => {
    const discordId = req.query.discordId;
    if (discordId) {
        res.redirect(`/?discordId=${discordId}`);
    } else {
        res.sendFile(path.join(__dirname, 'index.html'));
    }
});

// Spotify callback
app.get('/callback', async (req, res) => {
    const { code, state: discordUserId, error } = req.query;

    if (error) {
        console.error('❌ Spotify OAuth error:', error);
        return res.send(`
            <h1>❌ Error</h1>
            <p>Spotify authentication failed: ${error}</p>
            <a href="/">Try again</a>
        `);
    }

    if (!code || !discordUserId) {
        return res.send(`
            <h1>❌ Error</h1>
            <p>Missing authorization code or Discord ID</p>
            <a href="/">Try again</a>
        `);
    }

    try {
        console.log(`🔄 Processing callback for Discord user: ${discordUserId}`);

        // Exchange code for tokens
        const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64')}`
            },
            body: new URLSearchParams({
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: process.env.SPOTIFY_REDIRECT_URI
            })
        });

        if (!tokenResponse.ok) {
            throw new Error('Failed to exchange code for tokens');
        }

        const tokenData = await tokenResponse.json();

        // Save to database
        if (db) {
            const collection = db.collection('spotify_tokens');
            const userData = {
                discord_user_id: discordUserId,
                access_token: tokenData.access_token,
                refresh_token: tokenData.refresh_token,
                expires_at: Date.now() + tokenData.expires_in * 1000,
                updated_at: new Date(),
                created_at: new Date()
            };

            await collection.replaceOne(
                { discord_user_id: discordUserId }, 
                userData, 
                { upsert: true }
            );

            console.log(`✅ Saved tokens for Discord user: ${discordUserId}`);
        }

        // Redirect to success page
        res.redirect(`/success.html?discordId=${discordUserId}`);

    } catch (error) {
        console.error('❌ Callback processing error:', error);
        res.send(`
            <h1>❌ Error</h1>
            <p>Failed to process authentication: ${error.message}</p>
            <a href="/">Try again</a>
        `);
    }
});

// Health check
app.get('/health', async (req, res) => {
    try {
        let tokenCount = 0;
        if (db) {
            const collection = db.collection('spotify_tokens');
            tokenCount = await collection.countDocuments();
        }

        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            database: db ? 'connected' : 'disconnected',
            tokenCount: tokenCount
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Status endpoint
app.get('/status', async (req, res) => {
    try {
        let stats = { totalTokens: 0, recentTokens: 0 };
        
        if (db) {
            const collection = db.collection('spotify_tokens');
            stats.totalTokens = await collection.countDocuments();
            stats.recentTokens = await collection.countDocuments({
                updated_at: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
            });
        }

        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            service: 'Adore Spotify Web App',
            database: {
                status: db ? 'connected' : 'disconnected',
                ...stats
            },
            endpoints: {
                login: '/login?discordId=USER_ID',
                callback: '/callback',
                health: '/health',
                status: '/status'
            }
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Start server
async function startServer() {
    await connectDB();
    
    app.listen(PORT, '0.0.0.0', () => {
        console.log(`🎵 Spotify Web App running on port ${PORT}`);
        console.log(`🌐 Access at: http://${process.env.SERVER_HOST}:${PORT}`);
        console.log(`🔗 Login URL: http://${process.env.SERVER_HOST}:${PORT}/login?discordId=USER_ID`);
        console.log(`💚 Health check: http://${process.env.SERVER_HOST}:${PORT}/health`);
    });
}

startServer().catch(console.error);
