<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spotify Connected - Adore</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1db954, #1ed760);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .success-icon {
            font-size: 4em;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: white;
        }

        .user-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .steps {
            text-align: left;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .discord-btn {
            background: #5865f2;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .discord-btn:hover {
            background: #4752c4;
            transform: translateY(-2px);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>Successfully Connected!</h1>
        
        <div class="user-info">
            <h3>🎵 Spotify Account Connected</h3>
            <p id="userInfo">Setting up your connection...</p>
            <div class="loading" id="loading"></div>
        </div>

        <div class="steps">
            <h3>🎉 You're all set! Here's what you can do now:</h3>
            <div class="step">
                <strong>1.</strong> Go back to Discord where you ran <code>,spotify login</code>
            </div>
            <div class="step">
                <strong>2.</strong> Try <code>,spotify</code> to see your current playing song
            </div>
            <div class="step">
                <strong>3.</strong> Use <code>,spotify play [song name]</code> to search and play music
            </div>
            <div class="step">
                <strong>4.</strong> Control playback with <code>,spotify pause</code>, <code>,spotify next</code>, etc.
            </div>
        </div>

        <p style="margin-top: 20px; opacity: 0.8;">
            Your Spotify account is now connected to the Adore Discord bot!<br>
            You can close this page and return to Discord.
        </p>
    </div>

    <script>
        // Get Discord ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const discordId = urlParams.get('discordId');
        
        if (discordId) {
            document.getElementById('userInfo').innerHTML = `
                <strong>Discord ID:</strong> ${discordId}<br>
                <span style="color: #90EE90;">✓ Tokens saved to database</span><br>
                <span style="color: #90EE90;">✓ Ready to use Spotify commands</span>
            `;
            document.getElementById('loading').style.display = 'none';
        } else {
            document.getElementById('userInfo').innerHTML = `
                <span style="color: #FFB6C1;">⚠ No Discord ID found</span><br>
                Connection may not work properly
            `;
            document.getElementById('loading').style.display = 'none';
        }

        // Auto-close after 10 seconds
        setTimeout(() => {
            const autoClose = confirm('Auto-closing in 5 seconds. Click OK to close now or Cancel to keep open.');
            if (autoClose) {
                window.close();
            }
        }, 10000);
    </script>
</body>
</html>
