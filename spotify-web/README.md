# 🎵 Spotify OAuth Website

A standalone Spotify OAuth website designed to work with Discord bots running on separate VPS servers.

## 🎯 Purpose

This website handles the Spotify OAuth flow and token extraction. It does NOT store tokens or provide APIs - its job ends at successfully authenticating users and showing a success message.

## 🚀 Quick Setup

### 1. Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-repo/spotify-oauth-website)

### 2. Configure Environment Variables

In your Vercel dashboard, add these environment variables:

\`\`\`env
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=https://yourdomain.com/api/callback
\`\`\`

### 3. Update Spotify App Settings

In your [Spotify Developer Dashboard](https://developer.spotify.com/dashboard/):

1. Go to your app settings
2. Add redirect URI: `https://yourdomain.com/api/callback`
3. Save changes

## 🔄 How It Works

### User Flow:
1. Discord bot sends user to: `https://yourdomain.com/login?discordId=USER_ID`
2. Website redirects to Spotify OAuth
3. User authorizes the app
4. Spotify redirects to `/callback` with authorization code
5. Website exchanges code for tokens
6. Success page is shown
7. Done! ✅

### API Endpoints:

- `GET /login?discordId=123` - Initiates OAuth flow
- `GET /callback` - Handles Spotify callback
- `GET /health` - Health check

## 🎮 Discord Bot Integration

Your Discord bot should generate login links like this:

\`\`\`javascript
const loginUrl = `https://yourdomain.com/login?discordId=${userId}`;
await interaction.reply(`🔗 Connect your Spotify: ${loginUrl}`);
\`\`\`

## 🔧 Local Development

\`\`\`bash
# Install dependencies
npm install

# Start development server
npm run dev

# Visit http://localhost:3000
\`\`\`

## 📁 Project Structure

\`\`\`
├── api/
│   ├── auth.js          # Login endpoint
│   ├── callback.js      # OAuth callback handler
│   └── health.js        # Health check
├── public/
│   ├── index.html       # Welcome page
│   ├── success.html     # Success page
│   ├── error.html       # Error page
│   └── style.css        # Styles
├── package.json
├── vercel.json          # Vercel configuration
└── .env.example         # Environment template
\`\`\`

## 🔐 Security Features

- ✅ Secure token exchange
- ✅ State parameter validation
- ✅ Error handling
- ✅ No token storage
- ✅ CORS headers
- ✅ Environment validation

## 🌐 Deployment

This website is optimized for Vercel's serverless functions but can be deployed anywhere that supports Node.js.

### Vercel (Recommended)
- Automatic deployments
- Built-in SSL
- Global CDN
- Serverless functions

### Other Platforms
- Netlify Functions
- AWS Lambda
- Railway
- Heroku

## 🆘 Troubleshooting

### Common Issues:

1. **"Missing client ID" error**
   - Check environment variables are set correctly

2. **"Redirect URI mismatch" error**
   - Ensure redirect URI in Spotify app matches your domain

3. **"Token exchange failed" error**
   - Verify client secret is correct

4. **CORS errors**
   - Check domain configuration

## 📞 Support

If you encounter issues:

1. Check the browser console for errors
2. Verify environment variables
3. Check Spotify app configuration
4. Review server logs in Vercel dashboard

---

🎉 **Your Spotify OAuth website is ready!**
