# Adore Spotify Web App

Simple web app for Spotify OAuth authentication for the Adore Discord bot.

## Files

- `index.html` - Main login page
- `success.html` - Success page after authentication
- `server.js` - Express server backend
- `database.js` - MongoDB helper functions
- `package.json` - Dependencies
- `.env` - Environment variables

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment:**
   - Edit `.env` file with your settings
   - Make sure `MONGODB_CONNECTION` points to your database
   - Update `SERVER_HOST` to your server address

3. **Start the server:**
   ```bash
   npm start
   ```

4. **Test the setup:**
   - Visit: `http://your-server:3000`
   - Health check: `http://your-server:3000/health`

## How it works

1. User visits `/login?discordId=USER_ID`
2. User clicks "Login with Spotify"
3. Spotify redirects to `/callback` with auth code
4. Server exchanges code for tokens
5. Tokens are saved to MongoDB `spotify_tokens` collection
6. User sees success page
7. Discord bot automatically syncs tokens from database

## Endpoints

- `GET /` - Main login page
- `GET /login?discordId=USER_ID` - Login with Discord ID pre-filled
- `GET /callback` - Spotify OAuth callback
- `GET /health` - Health check
- `GET /status` - Detailed status info

## Database

Tokens are stored in the `spotify_tokens` collection:

```javascript
{
  discord_user_id: "123456789",
  access_token: "BQA...",
  refresh_token: "AQA...",
  expires_at: 1640995200000,
  created_at: Date,
  updated_at: Date
}
```

## Deployment

1. Upload files to your server
2. Run `npm install`
3. Update `.env` with your server details
4. Start with `npm start` or use PM2:
   ```bash
   pm2 start server.js --name spotify-web
   ```

## Integration with Discord Bot

The Discord bot will automatically sync tokens from this database. Make sure both the web app and Discord bot use the same MongoDB connection string.
