<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adore Spotify Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1db954, #1ed760);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            font-size: 3em;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: white;
        }

        p {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .login-btn {
            background: #1db954;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .login-btn:hover {
            background: #1ed760;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .discord-id-input {
            width: 100%;
            padding: 15px;
            margin: 20px 0;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        .discord-id-input::placeholder {
            color: #666;
        }

        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎵</div>
        <h1>Adore Spotify</h1>
        <p>Connect your Spotify account to the Adore Discord bot</p>
        
        <input type="text" 
               id="discordId" 
               class="discord-id-input" 
               placeholder="Enter your Discord User ID">
        
        <br>
        
        <button class="login-btn" onclick="startLogin()">
            🎵 Login with Spotify
        </button>

        <div class="info">
            <strong>How to get your Discord ID:</strong><br>
            1. Enable Developer Mode in Discord Settings<br>
            2. Right-click your username<br>
            3. Click "Copy User ID"
        </div>
    </div>

    <script>
        // Get Discord ID from URL if provided
        const urlParams = new URLSearchParams(window.location.search);
        const discordIdFromUrl = urlParams.get('discordId');
        
        if (discordIdFromUrl) {
            document.getElementById('discordId').value = discordIdFromUrl;
        }

        function startLogin() {
            const discordId = document.getElementById('discordId').value.trim();
            
            if (!discordId) {
                alert('Please enter your Discord User ID');
                return;
            }

            // Redirect to Spotify OAuth
            const clientId = '786e5ea4a5a24561b1bdfccc8169282c';
            const redirectUri = 'http://us-la-01.wisp.uno:3000/callback';
            const scopes = [
                'user-read-private',
                'user-read-email',
                'user-read-currently-playing',
                'user-read-playback-state',
                'user-modify-playback-state',
                'user-read-recently-played',
                'playlist-read-private',
                'playlist-read-collaborative'
            ].join(' ');

            const authUrl = `https://accounts.spotify.com/authorize?` +
                `response_type=code&` +
                `client_id=${clientId}&` +
                `scope=${encodeURIComponent(scopes)}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `state=${discordId}&` +
                `show_dialog=true`;

            window.location.href = authUrl;
        }
    </script>
</body>
</html>
