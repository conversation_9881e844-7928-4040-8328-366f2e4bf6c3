# Spotify Web App Deployment Guide

This guide explains how to deploy the Spotify web app on a separate VPS server.

## Prerequisites

- VPS server with Node.js 18+ installed
- Domain name or public IP address
- Access to the shared MongoDB database
- Spotify app credentials

## Deployment Steps

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Create app directory
sudo mkdir -p /var/www/spotify-web
sudo chown $USER:$USER /var/www/spotify-web
```

### 2. Upload Application Files

```bash
# Copy the spotify-web folder to your server
# You can use scp, rsync, or git clone

# Example using scp:
scp -r ./spotify-web/* user@your-server:/var/www/spotify-web/

# Or clone from repository:
cd /var/www/spotify-web
git clone <your-repo-url> .
```

### 3. Install Dependencies

```bash
cd /var/www/spotify-web
npm install
```

### 4. Environment Configuration

Create `.env` file:
```bash
nano .env
```

Add the following configuration:
```env
# Spotify API credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
SPOTIFY_REDIRECT_URI=https://your-domain.com:3000/callback

# Database connection (shared with Discord bot)
MONGODB_URI=mongodb://your-database-server:27017/adore

# Server settings
PORT=3000
NODE_ENV=production
```

### 5. Firewall Configuration

```bash
# Allow port 3000 for the web app
sudo ufw allow 3000

# If using nginx as reverse proxy (recommended)
sudo ufw allow 80
sudo ufw allow 443
```

### 6. SSL Certificate (Recommended)

For production, use SSL with Let's Encrypt:

```bash
# Install certbot
sudo apt install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d your-domain.com

# Note the certificate paths for nginx configuration
```

### 7. Nginx Reverse Proxy (Optional but Recommended)

Install and configure nginx:

```bash
sudo apt install nginx

# Create nginx configuration
sudo nano /etc/nginx/sites-available/spotify-web
```

Add this configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/spotify-web /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 8. Start the Application

Using PM2 for production:

```bash
cd /var/www/spotify-web

# Start with PM2
pm2 start backend/server.js --name "spotify-web"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### 9. Test the Deployment

```bash
# Check if the app is running
pm2 status

# Test the health endpoint
curl http://localhost:3000/health

# Check logs
pm2 logs spotify-web
```

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `SPOTIFY_CLIENT_ID` | Spotify app client ID | `abc123...` |
| `SPOTIFY_CLIENT_SECRET` | Spotify app client secret | `def456...` |
| `SPOTIFY_REDIRECT_URI` | OAuth callback URL | `https://your-domain.com:3000/callback` |
| `MONGODB_URI` | Database connection string | `mongodb://db-server:27017/adore` |
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment mode | `production` |

## Monitoring and Maintenance

### PM2 Commands
```bash
# View status
pm2 status

# View logs
pm2 logs spotify-web

# Restart app
pm2 restart spotify-web

# Stop app
pm2 stop spotify-web

# Monitor resources
pm2 monit
```

### Log Files
- Application logs: `pm2 logs spotify-web`
- Nginx logs: `/var/log/nginx/access.log` and `/var/log/nginx/error.log`

### Health Check
The app provides a health check endpoint at `/health`:
```bash
curl https://your-domain.com/health
```

Should return:
```json
{"status":"OK","timestamp":"2024-01-01T00:00:00.000Z"}
```

## Troubleshooting

### Common Issues

1. **Port 3000 already in use**
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **Database connection failed**
   - Check MongoDB server is running
   - Verify connection string in `.env`
   - Ensure firewall allows database connections

3. **SSL certificate issues**
   ```bash
   sudo certbot renew --dry-run
   ```

4. **Nginx configuration errors**
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

### Updating the Application

```bash
cd /var/www/spotify-web

# Pull latest changes (if using git)
git pull

# Install new dependencies
npm install

# Restart the application
pm2 restart spotify-web
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **Database Access**: Use MongoDB authentication and restrict IP access
3. **SSL**: Always use HTTPS in production
4. **Firewall**: Only open necessary ports
5. **Updates**: Keep Node.js and dependencies updated

## Integration with Discord Bot

Once deployed, update your Discord bot's `.env` file:
```env
SPOTIFY_WEB_URL=https://your-domain.com
SPOTIFY_WEB_MONGODB_URI=mongodb://your-database-server:27017/adore
```

The Discord bot will automatically connect to this web app for token synchronization.
