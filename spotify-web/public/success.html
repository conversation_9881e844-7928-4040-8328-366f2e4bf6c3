<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Successfully Connected!</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="card success-card">
            <div class="success-header">
                <div class="success-icon">✅</div>
                <h1>Successfully Connected!</h1>
                <p>Your Spotify account has been linked to the Discord bot.</p>
            </div>

            <div class="success-details">
                <div class="detail-item">
                    <span class="detail-icon">🔐</span>
                    <div>
                        <h3>Secure Connection</h3>
                        <p>Your tokens are securely processed</p>
                    </div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-icon">🔄</span>
                    <div>
                        <h3>Auto-Refresh</h3>
                        <p>Tokens will be automatically refreshed</p>
                    </div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-icon">🎵</span>
                    <div>
                        <h3>Ready to Use</h3>
                        <p>Bot can now access your Spotify data</p>
                    </div>
                </div>
            </div>

            <div class="next-steps">
                <h3>What's Next?</h3>
                <ul>
                    <li>🎮 Return to Discord and try music commands</li>
                    <li>🎵 Use <code>/current-track</code> to see what you're playing</li>
                    <li>📱 Use <code>/playlists</code> to browse your playlists</li>
                    <li>🔧 Use <code>/spotify-profile</code> to view your profile</li>
                </ul>
            </div>

            <div class="actions">
                <button onclick="window.close()" class="close-btn">Close Window</button>
                <button onclick="copyDiscordId()" class="copy-btn" id="copyBtn" style="display: none;">Copy Discord ID</button>
            </div>

            <div class="footer">
                <p>🔒 Your connection is secure and can be revoked anytime from your Spotify account settings.</p>
            </div>
        </div>
    </div>

    <script>
        // Get Discord ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const discordId = urlParams.get('discordId');

        if (discordId) {
            // Show copy button if Discord ID is present
            document.getElementById('copyBtn').style.display = 'inline-block';
        }

        function copyDiscordId() {
            if (discordId) {
                navigator.clipboard.writeText(discordId).then(() => {
                    const btn = document.getElementById('copyBtn');
                    const originalText = btn.textContent;
                    btn.textContent = 'Copied!';
                    btn.style.background = '#28a745';
                    
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = '';
                    }, 2000);
                });
            }
        }

        // Auto-close after 10 seconds (optional)
        setTimeout(() => {
            const shouldAutoClose = confirm('Connection successful! Close this window?');
            if (shouldAutoClose) {
                window.close();
            }
        }, 10000);
    </script>
</body>
</html>
