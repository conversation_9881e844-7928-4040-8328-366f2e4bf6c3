* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #1db954, #1ed760);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 600px;
}

.card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.header h1 {
  color: #1db954;
  margin-bottom: 10px;
  font-size: 2.5em;
  font-weight: 700;
}

.header p {
  color: #666;
  font-size: 1.1em;
  margin-bottom: 30px;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.feature {
  padding: 20px;
  border-radius: 15px;
  background: #f8f9fa;
  transition: transform 0.2s;
}

.feature:hover {
  transform: translateY(-2px);
}

.feature .icon {
  font-size: 2em;
  display: block;
  margin-bottom: 10px;
}

.feature h3 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1.1em;
  font-weight: 600;
}

.feature p {
  color: #666;
  font-size: 0.9em;
}

.info {
  background: #e8f5e8;
  padding: 20px;
  border-radius: 15px;
  margin: 30px 0;
  border-left: 4px solid #1db954;
}

.info p {
  color: #2d5a2d;
  margin-bottom: 10px;
  text-align: left;
}

.info p:last-child {
  margin-bottom: 0;
}

.info code {
  background: #d4edda;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 0.9em;
}

/* Success page styles */
.success-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.success-header .success-icon {
  font-size: 4em;
  margin-bottom: 20px;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-header h1 {
  color: #28a745;
  margin-bottom: 10px;
}

.success-details {
  margin: 30px 0;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  margin-bottom: 10px;
  background: white;
  border-radius: 10px;
  text-align: left;
}

.detail-icon {
  font-size: 1.5em;
  min-width: 40px;
}

.detail-item h3 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1em;
}

.detail-item p {
  color: #666;
  font-size: 0.9em;
}

.next-steps {
  text-align: left;
  margin: 30px 0;
  background: white;
  padding: 20px;
  border-radius: 15px;
}

.next-steps h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: 8px 0;
  color: #666;
}

.next-steps code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 0.9em;
  color: #1db954;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 30px 0;
}

.close-btn,
.copy-btn,
.retry-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 1em;
}

.close-btn {
  background: #6c757d;
  color: white;
}

.close-btn:hover {
  background: #545b62;
  transform: translateY(-1px);
}

.copy-btn {
  background: #007bff;
  color: white;
}

.copy-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.retry-btn {
  background: #1db954;
  color: white;
}

.retry-btn:hover {
  background: #1ed760;
  transform: translateY(-1px);
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer p {
  color: #666;
  font-size: 0.9em;
}

/* Error page styles */
.error-card {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.error-header .error-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.error-header h1 {
  color: #dc3545;
}

.error-message {
  background: white;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  border-left: 4px solid #dc3545;
  text-align: left;
}

.troubleshooting {
  text-align: left;
  margin: 30px 0;
  background: white;
  padding: 20px;
  border-radius: 15px;
}

.troubleshooting h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.troubleshooting ul {
  list-style: none;
  padding: 0;
}

.troubleshooting li {
  padding: 8px 0;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .card {
    padding: 20px;
  }

  .header h1 {
    font-size: 2em;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .detail-item {
    flex-direction: column;
    text-align: center;
  }

  .actions {
    flex-direction: column;
  }

  .actions button {
    width: 100%;
  }
}
