<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Error</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="card error-card">
            <div class="error-header">
                <div class="error-icon">❌</div>
                <h1>Connection Failed</h1>
                <p>There was an issue connecting your Spotify account.</p>
            </div>

            <div class="error-details" id="errorDetails">
                <p>Please try again or contact support if the issue persists.</p>
            </div>

            <div class="troubleshooting">
                <h3>Troubleshooting:</h3>
                <ul>
                    <li>🔄 Try the connection process again</li>
                    <li>🌐 Check your internet connection</li>
                    <li>🔐 Make sure you're logged into Spotify</li>
                    <li>🤖 Contact the Discord bot administrator</li>
                </ul>
            </div>

            <div class="actions">
                <button onclick="history.back()" class="retry-btn">Try Again</button>
                <button onclick="window.close()" class="close-btn">Close Window</button>
            </div>
        </div>
    </div>

    <script>
        // Display error details from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');

        const errorMessages = {
            'access_denied': 'You denied access to your Spotify account.',
            'missing_parameters': 'Invalid request parameters.',
            'server_configuration': 'Server configuration error.',
            'token_exchange_failed': 'Failed to exchange authorization code.',
            'processing_failed': 'Failed to process your request.'
        };

        if (error) {
            const errorMessage = errorMessages[error] || `Unknown error: ${error}`;
            document.getElementById('errorDetails').innerHTML = `
                <div class="error-message">
                    <strong>Error:</strong> ${errorMessage}
                </div>
            `;
        }
    </script>
</body>
</html>
