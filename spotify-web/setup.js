#!/usr/bin/env node

/**
 * Setup script for Spotify Web App
 * This script helps configure the web app for deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function createEnvFile() {
    const envPath = path.join(__dirname, '.env');
    
    if (fs.existsSync(envPath)) {
        console.log('⚠️ .env file already exists. Skipping creation.');
        return;
    }

    const envTemplate = `# Spotify Web App Configuration
# Copy this file to .env and fill in your values

# Spotify API Credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=https://your-domain.com:3000/callback

# Database Configuration (shared with Discord bot)
MONGODB_URI=mongodb://your-database-server:27017/adore

# Server Configuration
PORT=3000
NODE_ENV=production

# Optional: Logging level
LOG_LEVEL=info
`;

    fs.writeFileSync(envPath, envTemplate);
    console.log('✅ Created .env template file');
    console.log('📝 Please edit .env and fill in your configuration values');
}

function createLogsDirectory() {
    const logsDir = path.join(__dirname, 'logs');
    
    if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
        console.log('✅ Created logs directory');
    } else {
        console.log('📁 Logs directory already exists');
    }
}

function updatePackageJson() {
    const packagePath = path.join(__dirname, 'package.json');
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // Add production scripts if they don't exist
        if (!packageJson.scripts) {
            packageJson.scripts = {};
        }
        
        const productionScripts = {
            'start:prod': 'NODE_ENV=production node backend/server.js',
            'start:pm2': 'pm2 start ecosystem.config.js --env production',
            'stop:pm2': 'pm2 stop spotify-web',
            'restart:pm2': 'pm2 restart spotify-web',
            'logs:pm2': 'pm2 logs spotify-web',
            'test:env': 'node test-web-app.js'
        };
        
        let updated = false;
        for (const [script, command] of Object.entries(productionScripts)) {
            if (!packageJson.scripts[script]) {
                packageJson.scripts[script] = command;
                updated = true;
            }
        }
        
        if (updated) {
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            console.log('✅ Updated package.json with production scripts');
        } else {
            console.log('📦 Package.json scripts are up to date');
        }
        
    } catch (error) {
        console.log('⚠️ Could not update package.json:', error.message);
    }
}

function createNginxConfig() {
    const nginxConfig = `# Nginx configuration for Spotify Web App
# Save this as /etc/nginx/sites-available/spotify-web

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    # SSL Configuration (update paths as needed)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Proxy to Node.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
`;

    const nginxPath = path.join(__dirname, 'nginx.conf');
    fs.writeFileSync(nginxPath, nginxConfig);
    console.log('✅ Created nginx.conf template');
    console.log('📝 Copy this to /etc/nginx/sites-available/spotify-web on your server');
}

function displaySetupInstructions() {
    console.log('\n🎉 Setup completed! Next steps:\n');
    
    console.log('📋 Configuration:');
    console.log('   1. Edit .env file with your Spotify app credentials');
    console.log('   2. Update MONGODB_URI with your database connection string');
    console.log('   3. Set SPOTIFY_REDIRECT_URI to your domain + :3000/callback\n');
    
    console.log('🚀 Deployment:');
    console.log('   1. Upload files to your VPS server');
    console.log('   2. Run: npm install');
    console.log('   3. Test configuration: npm run test:env');
    console.log('   4. Start with PM2: npm run start:pm2\n');
    
    console.log('🔒 Security (recommended):');
    console.log('   1. Set up nginx reverse proxy (use nginx.conf template)');
    console.log('   2. Get SSL certificate: sudo certbot --nginx -d your-domain.com');
    console.log('   3. Configure firewall: sudo ufw allow 80,443/tcp\n');
    
    console.log('🔗 Integration:');
    console.log('   1. Update Discord bot .env with SPOTIFY_WEB_URL=https://your-domain.com');
    console.log('   2. Ensure both servers can access the same MongoDB database');
    console.log('   3. Test with: ,spotify login in Discord\n');
    
    console.log('📊 Monitoring:');
    console.log('   • Health check: curl https://your-domain.com/health');
    console.log('   • PM2 status: pm2 status');
    console.log('   • View logs: pm2 logs spotify-web');
}

async function setup() {
    console.log('🎵 Spotify Web App Setup\n');
    
    createEnvFile();
    createLogsDirectory();
    updatePackageJson();
    createNginxConfig();
    
    displaySetupInstructions();
}

// Run setup
setup().catch(error => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
});
