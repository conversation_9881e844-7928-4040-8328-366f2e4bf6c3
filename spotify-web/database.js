const { MongoClient } = require('mongodb');
require('dotenv').config();

let client = null;
let db = null;

async function connect() {
    if (db) return db;
    
    try {
        client = new MongoClient(process.env.MONGODB_CONNECTION);
        await client.connect();
        db = client.db();
        console.log('✅ Database connected');
        return db;
    } catch (error) {
        console.error('❌ Database connection failed:', error);
        throw error;
    }
}

async function saveSpotifyTokens(discordUserId, tokenData) {
    const database = await connect();
    const collection = database.collection('spotify_tokens');
    
    const userData = {
        discord_user_id: discordUserId,
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_at: Date.now() + tokenData.expires_in * 1000,
        updated_at: new Date(),
        created_at: new Date()
    };
    
    await collection.replaceOne(
        { discord_user_id: discordUserId }, 
        userData, 
        { upsert: true }
    );
    
    console.log(`💾 Saved tokens for Discord user: ${discordUserId}`);
    return userData;
}

async function getSpotifyTokens(discordUserId) {
    const database = await connect();
    const collection = database.collection('spotify_tokens');
    return await collection.findOne({ discord_user_id: discordUserId });
}

async function close() {
    if (client) {
        await client.close();
        client = null;
        db = null;
        console.log('🔌 Database connection closed');
    }
}

module.exports = {
    connect,
    saveSpotifyTokens,
    getSpotifyTokens,
    close
};
