// Spotify OAuth configuration
const SPOTIFY_CLIENT_ID = process.env.SPOTIFY_CLIENT_ID
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET
const SPOTIFY_REDIRECT_URI = process.env.SPOTIFY_REDIRECT_URI || "https://yourdomain.com/api/callback"

// Spotify scopes for Discord bot integration
const SPOTIFY_SCOPES = [
  "user-read-private",
  "user-read-email",
  "user-read-currently-playing",
  "user-read-playback-state",
  "user-modify-playback-state",
  "user-read-recently-played",
  "playlist-read-private",
  "playlist-read-collaborative",
  "playlist-modify-public",
  "playlist-modify-private",
].join(" ")

export default async function handler(req, res) {
  const { method, query } = req

  // Handle CORS
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type")

  if (method === "OPTIONS") {
    return res.status(200).end()
  }

  try {
    if (method === "GET") {
      const { action, discordId } = query

      if (action === "login") {
        // Handle login initiation
        if (!discordId) {
          return res.status(400).json({ error: "Missing discordId parameter" })
        }

        if (!SPOTIFY_CLIENT_ID) {
          return res.status(500).json({ error: "Spotify client ID not configured" })
        }

        console.log(`🎵 Initiating Spotify login for Discord user: ${discordId}`)

        // Build Spotify OAuth URL
        const authParams = new URLSearchParams({
          client_id: SPOTIFY_CLIENT_ID,
          response_type: "code",
          redirect_uri: SPOTIFY_REDIRECT_URI,
          scope: SPOTIFY_SCOPES,
          state: discordId,
          show_dialog: "true",
        })

        const authUrl = `https://accounts.spotify.com/authorize?${authParams.toString()}`

        // Redirect to Spotify
        return res.redirect(302, authUrl)
      }

      // Default response for unknown actions
      return res.status(400).json({ error: "Invalid action parameter" })
    }

    return res.status(405).json({ error: "Method not allowed" })
  } catch (error) {
    console.error("Auth handler error:", error)
    return res.status(500).json({ error: "Internal server error" })
  }
}
