import fetch from "node-fetch"

const SPOTIFY_CLIENT_ID = process.env.SPOTIFY_CLIENT_ID
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET
const SPOTIFY_REDIRECT_URI = process.env.SPOTIFY_REDIRECT_URI || "https://yourdomain.com/api/callback"

export default async function handler(req, res) {
  const { method, query } = req

  // Handle CORS
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type")

  if (method === "OPTIONS") {
    return res.status(200).end()
  }

  if (method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" })
  }

  try {
    const { code, state: discordUserId, error } = query

    // Handle OAuth errors
    if (error) {
      console.error("❌ Spotify OAuth error:", error)
      return res.redirect(302, `/error.html?error=${encodeURIComponent(error)}`)
    }

    // Validate required parameters
    if (!code || !discordUserId) {
      console.error("❌ Missing code or state parameter")
      return res.redirect(302, "/error.html?error=missing_parameters")
    }

    // Validate environment variables
    if (!SPOTIFY_CLIENT_ID || !SPOTIFY_CLIENT_SECRET) {
      console.error("❌ Missing Spotify credentials")
      return res.redirect(302, "/error.html?error=server_configuration")
    }

    console.log(`🔄 Processing callback for Discord user: ${discordUserId}`)

    // Exchange authorization code for tokens
    const tokenResponse = await fetch("https://accounts.spotify.com/api/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        code: code,
        redirect_uri: SPOTIFY_REDIRECT_URI,
      }),
    })

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.error("❌ Token exchange failed:", errorText)
      return res.redirect(302, "/error.html?error=token_exchange_failed")
    }

    const tokenData = await tokenResponse.json()

    // Calculate expiry time
    const expiresAt = Date.now() + tokenData.expires_in * 1000

    console.log(`✅ Successfully authenticated Discord user: ${discordUserId}`)
    console.log(`🔑 Token expires at: ${new Date(expiresAt).toISOString()}`)

    // For debugging (remove in production)
    console.log("Token data received:", {
      access_token: tokenData.access_token ? "***PRESENT***" : "MISSING",
      refresh_token: tokenData.refresh_token ? "***PRESENT***" : "MISSING",
      expires_in: tokenData.expires_in,
      expires_at: expiresAt,
    })

    // TODO: In the future, you can send this data to your Discord bot VPS
    // Example:
    // await fetch('https://your-discord-bot-vps.com/api/spotify-tokens', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     discordUserId,
    //     accessToken: tokenData.access_token,
    //     refreshToken: tokenData.refresh_token,
    //     expiresAt
    //   })
    // });

    // Redirect to success page
    return res.redirect(302, `/success.html?discordId=${discordUserId}`)
  } catch (error) {
    console.error("❌ Callback processing error:", error)
    return res.redirect(302, "/error.html?error=processing_failed")
  }
}
