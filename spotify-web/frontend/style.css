* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #1db954, #1ed760);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 600px;
}

.card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.header h1 {
  color: #1db954;
  margin-bottom: 10px;
  font-size: 2.5em;
}

.header p {
  color: #666;
  font-size: 1.1em;
  margin-bottom: 30px;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.feature {
  padding: 20px;
  border-radius: 15px;
  background: #f8f9fa;
}

.feature .icon {
  font-size: 2em;
  display: block;
  margin-bottom: 10px;
}

.feature h3 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1.1em;
}

.feature p {
  color: #666;
  font-size: 0.9em;
}

.login-section {
  margin: 30px 0;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.input-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1em;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #1db954;
}

.input-group small {
  color: #666;
  font-size: 0.9em;
}

.input-group small a {
  color: #1db954;
  text-decoration: none;
}

.spotify-btn {
  background: #1db954;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.spotify-btn:hover {
  background: #1ed760;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(29, 185, 84, 0.3);
}

.spotify-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spotify-icon {
  font-size: 1.2em;
}

.help-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
  text-align: left;
}

.help-section h4 {
  color: #333;
  margin-bottom: 10px;
}

.help-section ol {
  color: #666;
  padding-left: 20px;
}

.help-section li {
  margin-bottom: 5px;
}

/* Success page styles */
.success-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.success-header .success-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.success-header h1 {
  color: #28a745;
}

.user-info {
  margin: 30px 0;
  padding: 20px;
  background: white;
  border-radius: 15px;
}

.profile {
  display: flex;
  align-items: center;
  gap: 20px;
  text-align: left;
}

.profile-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-details h3 {
  color: #333;
  margin-bottom: 5px;
}

.profile-details p {
  color: #666;
  margin-bottom: 3px;
}

.next-steps {
  text-align: left;
  margin: 30px 0;
}

.next-steps h3 {
  color: #333;
  margin-bottom: 15px;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: 8px 0;
  color: #666;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-btn,
.close-btn,
.retry-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.test-btn {
  background: #007bff;
  color: white;
}

.test-btn:hover {
  background: #0056b3;
}

.close-btn,
.retry-btn {
  background: #6c757d;
  color: white;
}

.close-btn:hover,
.retry-btn:hover {
  background: #545b62;
}

.test-results {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  text-align: left;
}

.test-results h4 {
  color: #333;
  margin-bottom: 10px;
}

.test-results ul {
  list-style: none;
  padding: 0;
}

.test-results li {
  padding: 5px 0;
  color: #666;
}

/* Error page styles */
.error-card {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.error-header .error-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.error-header h1 {
  color: #dc3545;
}

.error-message {
  background: white;
  padding: 15px;
  border-radius: 10px;
  margin: 20px 0;
  border-left: 4px solid #dc3545;
}

.loading {
  color: #666;
  font-style: italic;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #f5c6cb;
}

/* Responsive design */
@media (max-width: 768px) {
  .card {
    padding: 20px;
  }

  .header h1 {
    font-size: 2em;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .profile {
    flex-direction: column;
    text-align: center;
  }

  .actions {
    flex-direction: column;
  }
}
