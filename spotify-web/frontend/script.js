function showDiscordIdHelp() {
  const helpSection = document.getElementById("discordIdHelp")
  helpSection.style.display = helpSection.style.display === "none" ? "block" : "none"
}

function loginWithSpotify() {
  const discordIdInput = document.getElementById("discordId")
  const discordId = discordIdInput.value.trim()

  if (!discordId) {
    alert("Please enter your Discord User ID")
    discordIdInput.focus()
    return
  }

  // Validate Discord ID format (should be 17-19 digits)
  if (!/^\d{17,19}$/.test(discordId)) {
    alert("Invalid Discord User ID format. It should be 17-19 digits.")
    discordIdInput.focus()
    return
  }

  // Disable button and show loading state
  const loginBtn = document.getElementById("loginBtn")
  loginBtn.disabled = true
  loginBtn.innerHTML = '<span class="spotify-icon">⏳</span> Redirecting...'

  // Redirect to login endpoint
  window.location.href = `/login?discordId=${discordId}`
}

// Auto-focus on Discord ID input
window.addEventListener("load", () => {
  const discordIdInput = document.getElementById("discordId")
  if (discordIdInput) {
    discordIdInput.focus()
  }
})

// Handle Enter key press in Discord ID input
document.addEventListener("keypress", (e) => {
  if (e.key === "Enter" && document.activeElement.id === "discordId") {
    loginWithSpotify()
  }
})
