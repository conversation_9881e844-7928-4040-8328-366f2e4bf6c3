<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Successfully Connected!</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="card success-card">
            <div class="success-header">
                <div class="success-icon">✅</div>
                <h1>Successfully Connected!</h1>
                <p>Your Spotify account has been linked to the Discord bot.</p>
            </div>

            <div class="user-info" id="userInfo">
                <div class="loading">Loading your Spotify profile...</div>
            </div>

            <div class="next-steps">
                <h3>What's Next?</h3>
                <ul>
                    <li>🎵 Your bot can now access your Spotify data</li>
                    <li>🔄 Tokens will be automatically refreshed</li>
                    <li>🎮 Use Discord commands to control your music</li>
                    <li>📱 Your connection is secure and encrypted</li>
                </ul>
            </div>

            <div class="actions">
                <button onclick="testConnection()" class="test-btn">Test Connection</button>
                <button onclick="window.close()" class="close-btn">Close Window</button>
            </div>

            <div class="test-results" id="testResults" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Get Discord ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const discordId = urlParams.get('discordId');

        // Load user profile on page load
        window.addEventListener('load', async () => {
            if (discordId) {
                await loadUserProfile();
            }
        });

        async function loadUserProfile() {
            try {
                const response = await fetch(`/api/user/${discordId}/profile`);
                const profile = await response.json();

                if (response.ok) {
                    document.getElementById('userInfo').innerHTML = `
                        <div class="profile">
                            <img src="${profile.images?.[0]?.url || '/default-avatar.png'}" alt="Profile" class="profile-image">
                            <div class="profile-details">
                                <h3>${profile.display_name}</h3>
                                <p>@${profile.id}</p>
                                <p>${profile.followers?.total || 0} followers</p>
                                <p>Country: ${profile.country}</p>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(profile.error);
                }
            } catch (error) {
                document.getElementById('userInfo').innerHTML = `
                    <div class="error">Failed to load profile: ${error.message}</div>
                `;
            }
        }

        async function testConnection() {
            const testResults = document.getElementById('testResults');
            testResults.style.display = 'block';
            testResults.innerHTML = '<div class="loading">Testing connection...</div>';

            try {
                // Test current track
                const trackResponse = await fetch(`/api/user/${discordId}/current-track`);
                const trackData = await trackResponse.json();

                let results = '<h4>Connection Test Results:</h4><ul>';
                
                if (trackResponse.ok && trackData.item) {
                    results += `<li>✅ Currently playing: ${trackData.item.name} by ${trackData.item.artists[0].name}</li>`;
                } else {
                    results += '<li>ℹ️ No track currently playing</li>';
                }

                // Test playlists
                const playlistResponse = await fetch(`/api/user/${discordId}/playlists?limit=5`);
                const playlistData = await playlistResponse.json();

                if (playlistResponse.ok) {
                    results += `<li>✅ Found ${playlistData.total} playlists</li>`;
                    if (playlistData.items.length > 0) {
                        results += `<li>📱 Recent playlist: ${playlistData.items[0].name}</li>`;
                    }
                }

                results += '</ul>';
                testResults.innerHTML = results;

            } catch (error) {
                testResults.innerHTML = `<div class="error">Test failed: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
