import fetch from "node-fetch"
import { getUserTokens, updateAccessToken } from "../database/connection.js"

export class SpotifyAPI {
  constructor() {
    this.clientId = process.env.SPOTIFY_CLIENT_ID
    this.clientSecret = process.env.SPOTIFY_CLIENT_SECRET
    this.redirectUri = process.env.SPOTIFY_REDIRECT_URI
  }

  // Generate Spotify OAuth URL
  getAuthUrl(discordUserId) {
    const scopes = [
      "user-read-private",
      "user-read-email",
      "user-read-currently-playing",
      "user-read-playback-state",
      "user-modify-playback-state",
      "user-read-recently-played",
      "playlist-read-private",
      "playlist-read-collaborative",
    ].join(" ")

    const params = new URLSearchParams({
      client_id: this.clientId,
      response_type: "code",
      redirect_uri: this.redirectUri,
      scope: scopes,
      state: discordUserId,
      show_dialog: "true",
    })

    return `https://accounts.spotify.com/authorize?${params.toString()}`
  }

  // Exchange authorization code for tokens
  async exchangeCodeForTokens(code) {
    const response = await fetch("https://accounts.spotify.com/api/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        code: code,
        redirect_uri: this.redirectUri,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token exchange failed: ${error}`)
    }

    return await response.json()
  }

  // Refresh access token
  async refreshAccessToken(refreshToken) {
    const response = await fetch("https://accounts.spotify.com/api/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token refresh failed: ${error}`)
    }

    return await response.json()
  }

  // Get valid access token for user (with auto-refresh)
  async getValidAccessToken(discordUserId) {
    const userData = await getUserTokens(discordUserId)

    if (!userData) {
      throw new Error("User not found. Please login with Spotify first.")
    }

    // Check if token is expired (with 5 minute buffer)
    const now = Date.now()
    const expiresAt = userData.expires_at
    const bufferTime = 5 * 60 * 1000 // 5 minutes

    if (now >= expiresAt - bufferTime) {
      console.log(`🔄 Token expired for user ${discordUserId}, refreshing...`)

      try {
        const tokenData = await this.refreshAccessToken(userData.refresh_token)
        await updateAccessToken(discordUserId, tokenData.access_token, tokenData.expires_in)
        return tokenData.access_token
      } catch (error) {
        console.error("❌ Token refresh failed:", error)
        throw new Error("Failed to refresh token. User may need to re-authenticate.")
      }
    }

    return userData.access_token
  }

  // Make authenticated Spotify API request
  async makeSpotifyRequest(discordUserId, endpoint, options = {}) {
    const accessToken = await this.getValidAccessToken(discordUserId)

    const response = await fetch(`https://api.spotify.com/v1${endpoint}`, {
      ...options,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Spotify API request failed: ${error}`)
    }

    return await response.json()
  }

  // Example: Get user's current playing track
  async getCurrentTrack(discordUserId) {
    return await this.makeSpotifyRequest(discordUserId, "/me/player/currently-playing")
  }

  // Example: Get user's profile
  async getUserProfile(discordUserId) {
    return await this.makeSpotifyRequest(discordUserId, "/me")
  }

  // Example: Get user's playlists
  async getUserPlaylists(discordUserId, limit = 20) {
    return await this.makeSpotifyRequest(discordUserId, `/me/playlists?limit=${limit}`)
  }
}
