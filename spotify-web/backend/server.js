import express from "express"
import cors from "cors"
import path from "path"
import { fileURLToPath } from "url"
import dotenv from "dotenv"
import { connectToDatabase, saveUserTokens, closeConnection } from "../database/connection.js"
import { SpotifyAPI } from "./spotify-utils.js"

dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const port = process.env.PORT || 3000
const spotifyAPI = new SpotifyAPI()

// Middleware
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))
app.use(express.static(path.join(__dirname, "../frontend")))

// Connect to database on startup
connectToDatabase().catch(console.error)

// Routes

// Health check
app.get("/health", (req, res) => {
  res.json({ status: "OK", timestamp: new Date().toISOString() })
})

// Initiate Spotify OAuth flow
app.get("/login", (req, res) => {
  const { discordId } = req.query

  if (!discordId) {
    return res.status(400).json({
      error: "Missing discordId parameter",
    })
  }

  console.log(`🎵 Initiating Spotify login for Discord user: ${discordId}`)

  const authUrl = spotifyAPI.getAuthUrl(discordId)
  res.redirect(authUrl)
})

// Handle Spotify OAuth callback
app.get("/callback", async (req, res) => {
  const { code, state: discordUserId, error } = req.query

  if (error) {
    console.error("❌ Spotify OAuth error:", error)
    return res.redirect("/error.html?error=" + encodeURIComponent(error))
  }

  if (!code || !discordUserId) {
    return res.status(400).json({
      error: "Missing code or state parameter",
    })
  }

  try {
    console.log(`🔄 Processing callback for Discord user: ${discordUserId}`)

    // Exchange code for tokens
    const tokenData = await spotifyAPI.exchangeCodeForTokens(code)

    // Save tokens to database
    await saveUserTokens(discordUserId, tokenData)

    console.log(`✅ Successfully authenticated Discord user: ${discordUserId}`)

    // Redirect to success page
    res.redirect(`/success.html?discordId=${discordUserId}`)
  } catch (error) {
    console.error("❌ Callback processing error:", error)
    res.redirect("/error.html?error=" + encodeURIComponent("Authentication failed"))
  }
})

// API endpoint to check user authentication status
app.get("/api/user/:discordId/status", async (req, res) => {
  try {
    const { discordId } = req.params
    const accessToken = await spotifyAPI.getValidAccessToken(discordId)

    res.json({
      authenticated: true,
      hasValidToken: !!accessToken,
    })
  } catch (error) {
    res.json({
      authenticated: false,
      error: error.message,
    })
  }
})

// API endpoint to get user's Spotify profile
app.get("/api/user/:discordId/profile", async (req, res) => {
  try {
    const { discordId } = req.params
    const profile = await spotifyAPI.getUserProfile(discordId)

    res.json(profile)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// API endpoint to get user's current track
app.get("/api/user/:discordId/current-track", async (req, res) => {
  try {
    const { discordId } = req.params
    const track = await spotifyAPI.getCurrentTrack(discordId)

    res.json(track)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// API endpoint to get user's playlists
app.get("/api/user/:discordId/playlists", async (req, res) => {
  try {
    const { discordId } = req.params
    const { limit = 20 } = req.query
    const playlists = await spotifyAPI.getUserPlaylists(discordId, Number.parseInt(limit))

    res.json(playlists)
  } catch (error) {
    res.status(400).json({ error: error.message })
  }
})

// Serve frontend files
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "../frontend/index.html"))
})

// Error handling middleware
app.use((error, req, res, next) => {
  console.error("❌ Server error:", error)
  res.status(500).json({ error: "Internal server error" })
})

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Shutting down server...")
  await closeConnection()
  process.exit(0)
})

app.listen(port, () => {
  console.log(`🚀 Server running on port ${port}`)
  console.log(`🌐 Frontend: http://localhost:${port}`)
  console.log(`🔗 Login URL format: http://localhost:${port}/login?discordId=YOUR_DISCORD_ID`)
})
