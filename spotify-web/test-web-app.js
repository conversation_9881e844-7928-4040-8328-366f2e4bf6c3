#!/usr/bin/env node

/**
 * Test script for Spotify Web App
 * This script tests the web app components and database connectivity
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '.env') });

async function testWebApp() {
    console.log('🧪 Testing Spotify Web App...\n');

    // Test 1: Environment Variables
    console.log('1️⃣ Checking Web App Environment Variables:');
    const requiredEnvVars = [
        'SPOTIFY_CLIENT_ID',
        'SPOTIFY_CLIENT_SECRET',
        'SPOTIFY_REDIRECT_URI',
        'MONGODB_URI'
    ];

    let envVarsOk = true;
    for (const envVar of requiredEnvVars) {
        if (process.env[envVar]) {
            console.log(`   ✅ ${envVar}`);
        } else {
            console.log(`   ❌ ${envVar} - MISSING`);
            envVarsOk = false;
        }
    }

    // Check optional variables
    const optionalEnvVars = ['PORT', 'NODE_ENV'];
    console.log('\n   📋 Optional variables:');
    for (const envVar of optionalEnvVars) {
        if (process.env[envVar]) {
            console.log(`   ✅ ${envVar} = ${process.env[envVar]}`);
        } else {
            console.log(`   ⚠️ ${envVar} - Using default`);
        }
    }

    if (!envVarsOk) {
        console.log('\n❌ Some required environment variables are missing.');
        console.log('Please create a .env file with the required variables.');
        return;
    }

    // Test 2: MongoDB Connection
    console.log('\n2️⃣ Testing Database Connection:');
    try {
        const { MongoClient } = await import('mongodb');
        const client = new MongoClient(process.env.MONGODB_URI);
        await client.connect();
        const db = client.db();
        
        // Test collections
        const collections = await db.listCollections().toArray();
        const spotifyTokensExists = collections.some(col => col.name === 'spotify_tokens');
        
        console.log(`   ✅ Connected to database`);
        console.log(`   📊 Found ${collections.length} collections`);
        console.log(`   🎵 spotify_tokens collection: ${spotifyTokensExists ? 'exists' : 'will be created'}`);
        
        // Test token collection operations
        const collection = db.collection('spotify_tokens');
        const tokenCount = await collection.countDocuments();
        console.log(`   🔢 Current token count: ${tokenCount}`);
        
        await client.close();
    } catch (error) {
        console.log(`   ❌ Database connection failed: ${error.message}`);
        return;
    }

    // Test 3: Spotify API Credentials
    console.log('\n3️⃣ Testing Spotify API Credentials:');
    try {
        const response = await fetch('https://accounts.spotify.com/api/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64')}`
            },
            body: new URLSearchParams({
                grant_type: 'client_credentials'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log(`   ✅ Spotify API credentials are valid`);
            console.log(`   🔑 Successfully obtained client credentials token`);
        } else {
            console.log(`   ❌ Spotify API credentials test failed: ${response.status}`);
        }
    } catch (error) {
        console.log(`   ❌ Spotify API credentials test failed: ${error.message}`);
        return;
    }

    // Test 4: Web App Components
    console.log('\n4️⃣ Testing Web App Components:');
    try {
        // Test database connection module
        const { connectToDatabase } = await import('./database/connection.js');
        await connectToDatabase();
        console.log(`   ✅ Database connection module works`);

        // Test Spotify utilities
        const { SpotifyAPI } = await import('./backend/spotify-utils.js');
        const spotifyAPI = new SpotifyAPI();
        console.log(`   ✅ Spotify utilities loaded`);

        // Test auth URL generation
        const authUrl = spotifyAPI.getAuthUrl('test-user-123');
        if (authUrl.includes('accounts.spotify.com') && authUrl.includes('test-user-123')) {
            console.log(`   ✅ Auth URL generation works`);
        } else {
            console.log(`   ❌ Auth URL generation failed`);
        }

    } catch (error) {
        console.log(`   ❌ Web app component test failed: ${error.message}`);
        return;
    }

    // Test 5: Server Configuration
    console.log('\n5️⃣ Testing Server Configuration:');
    const port = process.env.PORT || 3000;
    const redirectUri = process.env.SPOTIFY_REDIRECT_URI;
    
    console.log(`   📡 Server port: ${port}`);
    console.log(`   🔗 Redirect URI: ${redirectUri}`);
    
    // Validate redirect URI format
    try {
        const url = new URL(redirectUri);
        if (url.pathname === '/callback') {
            console.log(`   ✅ Redirect URI format is valid`);
        } else {
            console.log(`   ⚠️ Redirect URI should end with /callback`);
        }
    } catch (error) {
        console.log(`   ❌ Invalid redirect URI format: ${error.message}`);
    }

    console.log('\n🎉 All web app tests passed!');
    console.log('\n📋 Ready for deployment:');
    console.log('   1. Upload files to your VPS server');
    console.log('   2. Install dependencies: npm install');
    console.log('   3. Start with PM2: pm2 start ecosystem.config.js --env production');
    console.log('   4. Configure nginx reverse proxy (optional but recommended)');
    console.log('   5. Set up SSL certificate for HTTPS');
    console.log('\n🔗 After deployment, test the endpoints:');
    console.log(`   • Health check: GET ${redirectUri.replace('/callback', '/health')}`);
    console.log(`   • Status: GET ${redirectUri.replace('/callback', '/status')}`);
    console.log(`   • Login: GET ${redirectUri.replace('/callback', '/login?discordId=USER_ID')}`);
}

// Run the test
testWebApp().catch(error => {
    console.error('\n💥 Web app test failed:', error);
    process.exit(1);
});
