// Simple build verification script
import { fileURLToPath } from "url"
import { dirname, join } from "path"
import { existsSync } from "fs"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const projectRoot = join(__dirname, "..")

// Check if all required files exist
const requiredFiles = [
  "backend/server.js",
  "backend/spotify-utils.js",
  "database/connection.js",
  "frontend/index.html",
  "frontend/success.html",
  "frontend/error.html",
  "frontend/style.css",
  "frontend/script.js",
  ".env",
  "package.json",
]

console.log("🔍 Verifying build files...")

let allFilesExist = true

for (const file of requiredFiles) {
  const filePath = join(projectRoot, file)
  if (existsSync(filePath)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
}

if (allFilesExist) {
  console.log("\n🎉 All required files are present!")
  console.log("📦 Build verification successful")
  process.exit(0)
} else {
  console.log("\n❌ Some required files are missing")
  console.log("🚨 Build verification failed")
  process.exit(1)
}
