// Production startup script
import dotenv from "dotenv"
import { fileURLToPath } from "url"
import { dirname, join } from "path"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
dotenv.config({ path: join(__dirname, "..", ".env") })

// Validate required environment variables
const requiredEnvVars = ["SPOTIFY_CLIENT_ID", "SPOTIFY_CLIENT_SECRET", "SPOTIFY_REDIRECT_URI", "MONGODB_URI"]

console.log("🎵 Spotify Web App - Starting up...")
console.log("🔍 Checking environment variables...")

let allEnvVarsPresent = true

for (const envVar of requiredEnvVars) {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar}`)
  } else {
    console.log(`❌ ${envVar} - MISSING`)
    allEnvVarsPresent = false
  }
}

// Check optional but recommended variables
const optionalEnvVars = ["PORT", "NODE_ENV"]
console.log("\n📋 Optional environment variables:")
for (const envVar of optionalEnvVars) {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} = ${process.env[envVar]}`)
  } else {
    console.log(`⚠️ ${envVar} - Using default`)
  }
}

if (!allEnvVarsPresent) {
  console.log("\n🚨 Missing required environment variables!")
  console.log("Please check your .env file and ensure all required variables are set.")
  console.log("\nRequired variables:")
  console.log("- SPOTIFY_CLIENT_ID: Your Spotify app client ID")
  console.log("- SPOTIFY_CLIENT_SECRET: Your Spotify app client secret")
  console.log("- SPOTIFY_REDIRECT_URI: OAuth callback URL (e.g., https://your-domain.com:3000/callback)")
  console.log("- MONGODB_URI: Database connection string (shared with Discord bot)")
  process.exit(1)
}

console.log("\n🎉 Environment validation successful!")
console.log(`🌐 Server will run on port: ${process.env.PORT || 3000}`)
console.log(`🔗 Callback URL: ${process.env.SPOTIFY_REDIRECT_URI}`)
console.log("🚀 Starting server...")

// Import and start the server
import("../backend/server.js")
