// Production startup script
import dotenv from "dotenv"
import { fileURLToPath } from "url"
import { dirname, join } from "path"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
dotenv.config({ path: join(__dirname, "..", ".env") })

// Validate required environment variables
const requiredEnvVars = ["SPOTIFY_CLIENT_ID", "SPOTIFY_CLIENT_SECRET", "SPOTIFY_REDIRECT_URI", "MONGODB_URI"]

console.log("🔍 Checking environment variables...")

let allEnvVarsPresent = true

for (const envVar of requiredEnvVars) {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar}`)
  } else {
    console.log(`❌ ${envVar} - MISSING`)
    allEnvVarsPresent = false
  }
}

if (!allEnvVarsPresent) {
  console.log("\n🚨 Missing required environment variables!")
  console.log("Please check your .env file and ensure all required variables are set.")
  process.exit(1)
}

console.log("\n🎉 Environment validation successful!")
console.log("🚀 Starting server...")

// Import and start the server
import("../backend/server.js")
