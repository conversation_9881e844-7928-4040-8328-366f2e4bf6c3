const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "volume",
    aliases: ['vol'],
    description: `Adjust current player volume`,
    usage: '{guildprefix}spotify volume [0-100]',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state to show current volume if no args
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // If no volume specified, show current volume
            if (!args.length) {
                const currentVolume = playback.device.volume_percent;
                return embeds.info(message, `🔊 Current volume: **${currentVolume}%**\n\nUse \`spotify volume [0-100]\` to change the volume.`);
            }

            const volume = parseInt(args[0]);
            
            // Validate volume range
            if (isNaN(volume) || volume < 0 || volume > 100) {
                return embeds.warn(message, `Volume must be a number between **0** and **100**\n\nExample: \`spotify volume 50\``);
            }

            // Set volume
            await spotifyAPI.makeRequest(message.author.id, `/me/player/volume?volume_percent=${volume}`, 'PUT');

            // Create volume emoji based on level
            let volumeEmoji = '🔇';
            if (volume > 0 && volume <= 33) {
                volumeEmoji = '🔈';
            } else if (volume > 33 && volume <= 66) {
                volumeEmoji = '🔉';
            } else if (volume > 66) {
                volumeEmoji = '🔊';
            }

            await embeds.success(message, `${volumeEmoji} Volume set to **${volume}%**`);

        } catch (error) {
            console.error('Spotify volume error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // Handle premium-only feature
            if (error.message.includes('Premium required')) {
                return embeds.warn(message, `Volume control requires **Spotify Premium**\n\nThis feature is only available for premium subscribers.`);
            }

            return embeds.deny(message, `Failed to set volume: ${error.message}`);
        }
    }
};
