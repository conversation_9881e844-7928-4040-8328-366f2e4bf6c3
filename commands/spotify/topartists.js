const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "topartists",
    aliases: ['artists'],
    description: `Show your top artists from Spotify`,
    usage: '{guildprefix}spotify topartists [short/medium/long]',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Parse time range
            let timeRange = 'medium_term'; // Default to 6 months
            if (args.length > 0) {
                const input = args[0].toLowerCase();
                if (input === 'short' || input === '4w' || input === 'month') {
                    timeRange = 'short_term';
                } else if (input === 'medium' || input === '6m' || input === 'months') {
                    timeRange = 'medium_term';
                } else if (input === 'long' || input === 'all' || input === 'alltime') {
                    timeRange = 'long_term';
                }
            }

            // Show loading message
            const loadingEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setDescription(`${emojis.loading} Loading your top artists...`);
            
            const loadingMessage = await message.channel.send({ embeds: [loadingEmbed] });

            try {
                // Get top artists
                const topArtists = await spotifyAPI.getTopItems(message.author.id, 'artists', timeRange, 10);
                
                if (!topArtists.items || !topArtists.items.length) {
                    await loadingMessage.edit({ 
                        embeds: [embeds.warn(message, `No top artists found for the selected time period\n\nTry listening to more music on Spotify!`, false)] 
                    });
                    return;
                }

                // Get user profile for display name
                const profile = await spotifyAPI.getUserProfile(message.author.id);

                // Create embed
                const artistsEmbed = new EmbedBuilder()
                    .setColor(colors.info)
                    .setTitle(`🎤 ${profile.display_name}'s Top Artists`)
                    .setDescription(`**${spotifyAPI.formatTimeRange(timeRange)}**\n\n`)
                    .setThumbnail(message.author.displayAvatarURL({ dynamic: true }));

                let artistList = '';
                topArtists.items.forEach((artist, index) => {
                    const genres = artist.genres.length > 0 ? artist.genres.slice(0, 2).join(', ') : 'No genres listed';
                    const followers = artist.followers.total.toLocaleString();
                    
                    artistList += `**${index + 1}.** [${artist.name}](${artist.external_urls.spotify})\n`;
                    artistList += `${genres} • ${followers} followers\n\n`;
                });

                artistsEmbed.setDescription(`**${spotifyAPI.formatTimeRange(timeRange)}**\n\n${artistList}`);
                
                // Add footer with additional info
                artistsEmbed.setFooter({ 
                    text: `Use "short", "medium", or "long" to change time period • Powered by Spotify` 
                });

                await loadingMessage.edit({ embeds: [artistsEmbed] });

            } catch (apiError) {
                console.error('Spotify top artists API error:', apiError);
                
                if (apiError.message.includes('authentication expired')) {
                    await loadingMessage.edit({ embeds: [embeds.warn(message, apiError.message, false)] });
                    return;
                }

                await loadingMessage.edit({ 
                    embeds: [embeds.deny(message, `Failed to get top artists: ${apiError.message}`, false)] 
                });
            }

        } catch (error) {
            console.error('Spotify top artists error:', error);
            return embeds.deny(message, `Failed to process top artists request: ${error.message}`);
        }
    }
};
