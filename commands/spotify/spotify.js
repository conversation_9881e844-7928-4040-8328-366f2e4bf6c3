const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { runHelpCommand } = require('../../utils/commandProcessor');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "spotify",
    aliases: ['sp'],
    description: `Spotify integration for music control and statistics`,
    usage: '{guildprefix}spotify [subcommand]\n{guildprefix}spotify [song name] - Search and play',
    cooldown: 3000,
    run: async (client, message, args) => {
        try {
            // If no arguments, show current playing song or help
            if (!args.length) {
                // Ensure user data exists
                await ensureUserData(message.author.id);

                // Check if user is authenticated
                const spotifyData = await userCache.getSpotifyData(message.author.id);
                if (!spotifyData?.accessToken) {
                    return showSpotifyHelp(message);
                }

                // Try to show current playing song
                try {
                    const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
                    if (!playback || !playback.item) {
                        return showSpotifyHelp(message);
                    }

                    return showCurrentTrack(message, playback);
                } catch (error) {
                    return showSpotifyHelp(message);
                }
            }

            const subcommand = args[0].toLowerCase();

            // Route to appropriate subcommand
            const subcommands = {
                'login': () => require('./login').run(client, message, args.slice(1)),
                'logout': () => require('./logout').run(client, message, args.slice(1)),
                'play': () => require('./play').run(client, message, args.slice(1)),
                'pause': () => require('./pause').run(client, message, args.slice(1)),
                'resume': () => require('./resume').run(client, message, args.slice(1)),
                'next': () => require('./next').run(client, message, args.slice(1)),
                'previous': () => require('./previous').run(client, message, args.slice(1)),
                'queue': () => require('./queue').run(client, message, args.slice(1)),
                'device': () => require('./device').run(client, message, args.slice(1)),
                'volume': () => require('./volume').run(client, message, args.slice(1)),
                'seek': () => require('./seek').run(client, message, args.slice(1)),
                'like': () => require('./like').run(client, message, args.slice(1)),
                'unlike': () => require('./unlike').run(client, message, args.slice(1)),
                'repeat': () => require('./repeat').run(client, message, args.slice(1)),
                'shuffle': () => require('./shuffle').run(client, message, args.slice(1)),
                'toptracks': () => require('./toptracks').run(client, message, args.slice(1)),
                'topartists': () => require('./topartists').run(client, message, args.slice(1))
            };

            // Add aliases
            const aliases = {
                'connect': 'login',
                'auth': 'login',
                'disconnect': 'logout',
                'unlink': 'logout',
                'stop': 'pause',
                'unpause': 'play',
                'skip': 'next',
                'forward': 'next',
                'prev': 'previous',
                'back': 'previous',
                'add': 'queue',
                'q': 'queue',
                'devices': 'device',
                'vol': 'volume',
                'position': 'seek',
                'save': 'like',
                'heart': 'like',
                'unsave': 'unlike',
                'unheart': 'unlike',
                'loop': 'repeat',
                'random': 'shuffle',
                'tracks': 'toptracks',
                'topsongs': 'toptracks',
                'artists': 'topartists'
            };

            // Check if it's a known subcommand or alias
            const commandToRun = subcommands[subcommand] || subcommands[aliases[subcommand]];
            
            if (commandToRun) {
                return await commandToRun();
            }

            // If not a subcommand, treat as search query
            return await require('./play').run(client, message, args);

        } catch (error) {
            console.error('Spotify main command error:', error);
            return embeds.deny(message, `Failed to process Spotify command: ${error.message}`);
        }
    }
};

/**
 * Show Spotify help/commands
 */
async function showSpotifyHelp(message) {
    const helpEmbed = new EmbedBuilder()
        .setColor(colors.info)
        .setTitle('🎵 Spotify Commands')
        .setDescription('Control your Spotify playback and view your music statistics')
        .addFields([
            {
                name: '🔐 Authentication',
                value: '`spotify login` - Connect your Spotify account\n`spotify logout` - Disconnect your account',
                inline: false
            },
            {
                name: '🎮 Playback Control',
                value: '`spotify play [song]` - Search and play a song\n`spotify pause` - Pause playback\n`spotify resume` - Resume playback\n`spotify next` - Skip to next song\n`spotify previous` - Go to previous song',
                inline: false
            },
            {
                name: '📋 Queue & Devices',
                value: '`spotify queue [song]` - Add song to queue\n`spotify device` - Change playback device\n`spotify device list` - List all devices',
                inline: false
            },
            {
                name: '🎛️ Controls',
                value: '`spotify volume [0-100]` - Set volume\n`spotify seek [time]` - Seek to position\n`spotify repeat [mode]` - Set repeat mode\n`spotify shuffle [on/off]` - Toggle shuffle',
                inline: false
            },
            {
                name: '💚 Library',
                value: '`spotify like` - Like current song\n`spotify unlike` - Unlike current song',
                inline: false
            },
            {
                name: '📊 Statistics',
                value: '`spotify toptracks [period]` - Your top tracks\n`spotify topartists [period]` - Your top artists',
                inline: false
            }
        ])
        .setFooter({ text: 'Use "spotify [song name]" to search and play music' });

    return message.channel.send({ embeds: [helpEmbed] });
}

/**
 * Show current playing track
 */
async function showCurrentTrack(message, playback) {
    const track = playback.item;
    const artists = track.artists.map(artist => artist.name).join(', ');
    const progress = spotifyAPI.formatDuration(playback.progress_ms);
    const duration = spotifyAPI.formatDuration(track.duration_ms);
    
    // Create progress bar
    const progressPercent = (playback.progress_ms / track.duration_ms) * 100;
    const progressBarLength = 20;
    const filledLength = Math.round((progressPercent / 100) * progressBarLength);
    const progressBar = '█'.repeat(filledLength) + '░'.repeat(progressBarLength - filledLength);

    const nowPlayingEmbed = new EmbedBuilder()
        .setColor(colors.info)
        .setTitle(playback.is_playing ? '🎵 Now Playing' : '⏸️ Paused')
        .setDescription(`**${track.name}**\nby **${artists}**`)
        .addFields([
            {
                name: '💿 Album',
                value: track.album.name,
                inline: true
            },
            {
                name: '📱 Device',
                value: playback.device.name,
                inline: true
            },
            {
                name: '🔊 Volume',
                value: `${playback.device.volume_percent}%`,
                inline: true
            },
            {
                name: '⏱️ Progress',
                value: `${progress} / ${duration}\n\`${progressBar}\``,
                inline: false
            },
            {
                name: '🔁 Repeat',
                value: playback.repeat_state === 'off' ? 'Off' : 
                       playback.repeat_state === 'track' ? 'Track' : 'Context',
                inline: true
            },
            {
                name: '🔀 Shuffle',
                value: playback.shuffle_state ? 'On' : 'Off',
                inline: true
            },
            {
                name: '🔗 Spotify',
                value: `[Open in Spotify](${track.external_urls.spotify})`,
                inline: true
            }
        ]);

    if (track.album.images && track.album.images.length > 0) {
        nowPlayingEmbed.setThumbnail(track.album.images[0].url);
    }

    return message.channel.send({ embeds: [nowPlayingEmbed] });
}
