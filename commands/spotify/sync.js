const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "sync",
    aliases: ['refresh'],
    description: `Manually sync your Spotify tokens from the web app`,
    usage: '{guildprefix}spotify sync',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Show loading message
            const loadingMsg = await embeds.info(message, `🔄 Syncing your Spotify connection from the web app...`);

            // Try to sync tokens from web app
            const synced = await spotifyAPI.syncTokensFromWebApp(message.author.id);

            if (synced) {
                // Get updated data to show user info
                try {
                    const profile = await spotifyAPI.getUserProfile(message.author.id);
                    await loadingMsg.edit({
                        embeds: [embeds.success(message, `✅ Successfully synced your Spotify connection!\n\nConnected as **${profile.display_name}**`, false)]
                    });
                } catch (error) {
                    await loadingMsg.edit({
                        embeds: [embeds.success(message, `✅ Successfully synced your Spotify tokens!\n\nYou can now use Spotify commands.`, false)]
                    });
                }
            } else {
                await loadingMsg.edit({
                    embeds: [embeds.warn(message, `❌ No Spotify connection found in the web app\n\nPlease use \`spotify login\` to connect your account first.`, false)]
                });
            }

        } catch (error) {
            console.error('Spotify sync error:', error);
            return embeds.deny(message, `Failed to sync Spotify connection: ${error.message}`);
        }
    }
};
