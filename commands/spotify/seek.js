const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "seek",
    aliases: ['position'],
    description: `Seek to a position in the current song`,
    usage: '{guildprefix}spotify seek [seconds]\n{guildprefix}spotify seek [MM:SS]',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            if (!args.length) {
                return embeds.warn(message, `Please specify a position to seek to\n\nExamples:\n\`spotify seek 30\` (30 seconds)\n\`spotify seek 1:30\` (1 minute 30 seconds)`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            if (!playback.item) {
                return embeds.warn(message, `No track is currently playing\n\nStart playing a song first.`);
            }

            let positionMs;
            const input = args[0];

            // Parse time input (MM:SS or seconds)
            if (input.includes(':')) {
                const parts = input.split(':');
                if (parts.length !== 2) {
                    return embeds.warn(message, `Invalid time format. Use MM:SS format\n\nExample: \`spotify seek 1:30\``);
                }
                
                const minutes = parseInt(parts[0]);
                const seconds = parseInt(parts[1]);
                
                if (isNaN(minutes) || isNaN(seconds) || seconds >= 60) {
                    return embeds.warn(message, `Invalid time format. Use MM:SS format\n\nExample: \`spotify seek 1:30\``);
                }
                
                positionMs = (minutes * 60 + seconds) * 1000;
            } else {
                const seconds = parseInt(input);
                if (isNaN(seconds)) {
                    return embeds.warn(message, `Invalid time format. Use seconds or MM:SS format\n\nExamples: \`spotify seek 30\` or \`spotify seek 1:30\``);
                }
                positionMs = seconds * 1000;
            }

            // Check if position is within track duration
            const trackDuration = playback.item.duration_ms;
            if (positionMs < 0 || positionMs >= trackDuration) {
                const maxTime = spotifyAPI.formatDuration(trackDuration);
                return embeds.warn(message, `Position must be between **0:00** and **${maxTime}**`);
            }

            // Seek to position
            await spotifyAPI.makeRequest(message.author.id, `/me/player/seek?position_ms=${positionMs}`, 'PUT');

            const track = playback.item;
            const artists = track.artists.map(artist => artist.name).join(', ');
            const seekTime = spotifyAPI.formatDuration(positionMs);

            await embeds.success(message, `⏩ Seeked to **${seekTime}** in **${track.name}** by **${artists}**`);

        } catch (error) {
            console.error('Spotify seek error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // Handle premium-only feature
            if (error.message.includes('Premium required')) {
                return embeds.warn(message, `Seek control requires **Spotify Premium**\n\nThis feature is only available for premium subscribers.`);
            }

            return embeds.deny(message, `Failed to seek: ${error.message}`);
        }
    }
};
