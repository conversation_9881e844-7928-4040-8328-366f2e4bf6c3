const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "resume",
    aliases: ['play', 'unpause'],
    description: `Resume the current song`,
    usage: '{guildprefix}spotify resume',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open on one of your devices.`);
            }

            if (playback.is_playing) {
                const track = playback.item;
                const artists = track.artists.map(artist => artist.name).join(', ');
                return embeds.warn(message, `**${track.name}** by **${artists}** is already playing\n\nUse \`spotify pause\` to pause playback.`);
            }

            // Resume playback
            await spotifyAPI.makeRequest(message.author.id, '/me/player/play', 'PUT');

            // Get track info for response
            const track = playback.item;
            const artists = track.artists.map(artist => artist.name).join(', ');

            await embeds.success(message, `▶️ Resumed **${track.name}** by **${artists}**`);

        } catch (error) {
            console.error('Spotify resume error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open on one of your devices.`);
            }

            return embeds.deny(message, `Failed to resume playback: ${error.message}`);
        }
    }
};
