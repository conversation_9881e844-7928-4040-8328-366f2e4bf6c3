const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "shuffle",
    aliases: ['random'],
    description: `Toggle playback shuffle`,
    usage: '{guildprefix}spotify shuffle [on/off]',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            let shuffleState;
            let displayText;
            let emoji;

            // If no argument provided, toggle current state
            if (!args.length) {
                shuffleState = !playback.shuffle_state;
            } else {
                const input = args[0].toLowerCase();
                if (input === 'on' || input === 'true' || input === 'enable' || input === '1') {
                    shuffleState = true;
                } else if (input === 'off' || input === 'false' || input === 'disable' || input === '0') {
                    shuffleState = false;
                } else {
                    return embeds.warn(message, `Invalid shuffle option. Use **on** or **off**\n\nExample: \`spotify shuffle on\``);
                }
            }

            // Set display text and emoji
            if (shuffleState) {
                displayText = 'On';
                emoji = '🔀';
            } else {
                displayText = 'Off';
                emoji = '➡️';
            }

            // Set shuffle mode
            await spotifyAPI.makeRequest(message.author.id, `/me/player/shuffle?state=${shuffleState}`, 'PUT');

            await embeds.success(message, `${emoji} Shuffle mode set to **${displayText}**`);

        } catch (error) {
            console.error('Spotify shuffle error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // Handle premium-only feature
            if (error.message.includes('Premium required')) {
                return embeds.warn(message, `Shuffle control requires **Spotify Premium**\n\nThis feature is only available for premium subscribers.`);
            }

            return embeds.deny(message, `Failed to set shuffle mode: ${error.message}`);
        }
    }
};
