const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "device",
    aliases: ['devices'],
    description: `Change the device you're listening on or list all devices`,
    usage: '{guildprefix}spotify device\n{guildprefix}spotify device list',
    cooldown: 3000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get user's devices
            const devices = await spotifyAPI.getDevices(message.author.id);
            
            if (!devices.length) {
                return embeds.warn(message, `No Spotify devices found\n\nMake sure Spotify is open on at least one of your devices.`);
            }

            // If user wants to list devices or no specific action
            if (!args.length || args[0].toLowerCase() === 'list') {
                const deviceEmbed = new EmbedBuilder()
                    .setColor(colors.info)
                    .setTitle('🎵 Your Spotify Devices')
                    .setDescription('Here are all your available Spotify devices:');

                let deviceList = '';
                devices.forEach((device, index) => {
                    const status = device.is_active ? '🟢 **Active**' : '⚪ Available';
                    const deviceType = device.type.charAt(0).toUpperCase() + device.type.slice(1).toLowerCase();
                    const volume = device.volume_percent !== null ? ` (${device.volume_percent}% volume)` : '';
                    
                    deviceList += `**${index + 1}.** ${device.name}\n`;
                    deviceList += `${status} • ${deviceType}${volume}\n\n`;
                });

                deviceEmbed.setDescription(deviceList);
                deviceEmbed.setFooter({ text: 'Use "spotify device [number]" to switch to a specific device' });

                return message.channel.send({ embeds: [deviceEmbed] });
            }

            // If user wants to switch to a specific device
            const deviceNumber = parseInt(args[0]);
            if (isNaN(deviceNumber) || deviceNumber < 1 || deviceNumber > devices.length) {
                return embeds.warn(message, `Invalid device number. Use \`spotify device list\` to see available devices.`);
            }

            const selectedDevice = devices[deviceNumber - 1];
            
            if (selectedDevice.is_active) {
                return embeds.warn(message, `**${selectedDevice.name}** is already your active device.`);
            }

            // Transfer playback to the selected device
            const transferData = {
                device_ids: [selectedDevice.id],
                play: false // Don't automatically start playing
            };

            await spotifyAPI.makeRequest(message.author.id, '/me/player', 'PUT', transferData);

            const deviceType = selectedDevice.type.charAt(0).toUpperCase() + selectedDevice.type.slice(1).toLowerCase();
            await embeds.success(message, `🎵 Switched to **${selectedDevice.name}** (${deviceType})`);

        } catch (error) {
            console.error('Spotify device error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }

            return embeds.deny(message, `Failed to manage devices: ${error.message}`);
        }
    }
};
