const { embeds } = require('../../core/embeds');
const { User, ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');
const { createConfirmation } = require('../../core/buttons');

module.exports = {
    name: "logout",
    aliases: ['disconnect', 'unlink'],
    description: `Disconnect your Spotify account from the bot`,
    usage: '{guildprefix}spotify logout',
    cooldown: 3000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is connected
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You don't have a Spotify account connected to **adore**\n\nUse \`spotify login\` to connect your account.`);
            }

            // Create confirmation
            await createConfirmation(
                message,
                `Are you sure you want to disconnect your Spotify account?\n\nThis will remove all stored Spotify data and you'll need to re-authorize to use Spotify commands again.`,
                async (interaction) => {
                    try {
                        // Clear Spotify data using cache method
                        await userCache.clearSpotifyData(message.author.id);

                        // Send success message
                        await embeds.success(message, `Successfully disconnected your Spotify account from **adore**\n\nTo completely remove access, visit your [Spotify account settings](https://www.spotify.com/account/apps/) and revoke access to **adore**.`);

                    } catch (error) {
                        console.error('Spotify logout error:', error);
                        await embeds.deny(message, `Failed to disconnect Spotify account: ${error.message}`);
                    }
                }
            );

        } catch (error) {
            console.error('Spotify logout error:', error);
            return embeds.deny(message, `Failed to process logout request: ${error.message}`);
        }
    }
};
