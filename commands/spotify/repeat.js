const { embeds } = require('../../core/embeds');
const spotifyAPI = require('../../utils/spotify');
const { ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "repeat",
    aliases: ['loop'],
    description: `Set repeat mode for playback`,
    usage: '{guildprefix}spotify repeat [off/track/context]',
    cooldown: 2000,
    run: async (client, message, args) => {
        try {
            // Check if user is authenticated
            // Ensure user data exists
            await ensureUserData(message.author.id);
            
            // Check if user is authenticated
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (!spotifyData?.accessToken) {
                return embeds.warn(message, `You need to connect your Spotify account first\n\nUse \`spotify login\` to get started.`);
            }

            // Get current playback state
            const playback = await spotifyAPI.getCurrentPlayback(message.author.id);
            if (!playback) {
                return embeds.warn(message, `No active Spotify session found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // If no mode specified, show current repeat state
            if (!args.length) {
                const currentRepeat = playback.repeat_state;
                let repeatText = '';
                let repeatEmoji = '';
                
                switch (currentRepeat) {
                    case 'off':
                        repeatText = 'Off';
                        repeatEmoji = '➡️';
                        break;
                    case 'track':
                        repeatText = 'Track (repeat current song)';
                        repeatEmoji = '🔂';
                        break;
                    case 'context':
                        repeatText = 'Context (repeat playlist/album)';
                        repeatEmoji = '🔁';
                        break;
                }

                return embeds.info(message, `${repeatEmoji} Current repeat mode: **${repeatText}**\n\nUse \`spotify repeat [off/track/context]\` to change the repeat mode.`);
            }

            const mode = args[0].toLowerCase();
            let repeatState = '';
            let displayText = '';
            let emoji = '';

            // Parse repeat mode
            switch (mode) {
                case 'off':
                case 'none':
                case 'disable':
                    repeatState = 'off';
                    displayText = 'Off';
                    emoji = '➡️';
                    break;
                case 'track':
                case 'song':
                case 'one':
                case '1':
                    repeatState = 'track';
                    displayText = 'Track (repeat current song)';
                    emoji = '🔂';
                    break;
                case 'context':
                case 'playlist':
                case 'album':
                case 'all':
                    repeatState = 'context';
                    displayText = 'Context (repeat playlist/album)';
                    emoji = '🔁';
                    break;
                default:
                    return embeds.warn(message, `Invalid repeat mode. Use one of:\n\n**off** - No repeat\n**track** - Repeat current song\n**context** - Repeat playlist/album`);
            }

            // Set repeat mode
            await spotifyAPI.makeRequest(message.author.id, `/me/player/repeat?state=${repeatState}`, 'PUT');

            await embeds.success(message, `${emoji} Repeat mode set to **${displayText}**`);

        } catch (error) {
            console.error('Spotify repeat error:', error);
            
            if (error.message.includes('authentication expired')) {
                return embeds.warn(message, error.message);
            }
            
            if (error.message.includes('No active device')) {
                return embeds.warn(message, `No active Spotify device found\n\nMake sure Spotify is open and playing music on one of your devices.`);
            }

            // Handle premium-only feature
            if (error.message.includes('Premium required')) {
                return embeds.warn(message, `Repeat control requires **Spotify Premium**\n\nThis feature is only available for premium subscribers.`);
            }

            return embeds.deny(message, `Failed to set repeat mode: ${error.message}`);
        }
    }
};
