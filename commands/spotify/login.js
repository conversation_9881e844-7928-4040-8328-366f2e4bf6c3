const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors, emojis } = require('../../config/setup');
const config = require('../../config/setup');
const spotifyAPI = require('../../utils/spotify');
const { User, ensureUserData } = require('../../database');
const userCache = require('../../database/cache/models/user');

module.exports = {
    name: "login",
    aliases: ['connect', 'auth'],
    description: `Connect your Spotify account to the bot`,
    usage: '{guildprefix}spotify login',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Ensure user data exists
            await ensureUserData(message.author.id);

            // Check if user is already connected
            const spotifyData = await userCache.getSpotifyData(message.author.id);
            if (spotifyData?.accessToken) {
                try {
                    const profile = await spotifyAPI.getUserProfile(message.author.id);
                    return embeds.warn(message, `You're already connected to Spotify as **${profile.display_name}**\n\nUse \`spotify logout\` to disconnect first.`);
                } catch (error) {
                    // If we can't get profile, token might be expired, continue with login
                }
            }

            // Use the web app for authentication
            const webAppURL = process.env.SPOTIFY_WEB_URL || 'http://localhost:3000';
            const loginURL = `${webAppURL}/login?discordId=${message.author.id}`;

            // Create authorization embed
            const authEmbed = new EmbedBuilder()
                .setColor(colors.info)
                .setTitle('🎵 Spotify Authorization')
                .setDescription(`Connect your Spotify account to **adore** to control your music and view statistics.`)
                .addFields([
                    {
                        name: '🚀 Simple Setup',
                        value: `**Step 1:** Click **"Login with Spotify"** below\n**Step 2:** Authorize **adore** on Spotify\n**Step 3:** You'll be automatically connected!\n**Done!** 🎉`,
                        inline: false
                    },
                    {
                        name: '✨ Features',
                        value: `• **Automatic token refresh** - no need to re-login\n• **Secure authentication** via Spotify OAuth\n• **Full playback control** and music statistics\n• Disconnect anytime with \`spotify logout\``,
                        inline: false
                    }
                ])
                .setFooter({ text: 'Click the button below to get started! 🎵' })
                .setTimestamp();

            // Create button for web app login
            const authButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Login with Spotify')
                        .setStyle(ButtonStyle.Link)
                        .setURL(loginURL)
                        .setEmoji('🎵')
                );

            await message.channel.send({
                embeds: [authEmbed],
                components: [authButtons]
            });

            // Add a follow-up message with instructions
            setTimeout(async () => {
                try {
                    await embeds.info(message, `After authorizing on the website, use \`spotify\` to check if you're connected!\n\nThe connection should happen automatically within a few seconds.`);
                } catch (error) {
                    // Channel might be unavailable
                }
            }, 2000);

        } catch (error) {
            console.error('Spotify login error:', error);
            return embeds.deny(message, `Failed to show login interface: ${error.message}`);
        }
    }
};
