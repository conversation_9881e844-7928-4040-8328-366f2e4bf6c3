const { embeds } = require('../../core/embeds');
const config = require('../../config/setup');

module.exports = {
    name: "spotify-sync-all",
    aliases: ['spotifysyncall', 'sync-all-spotify'],
    description: `Force sync all Spotify tokens from web app (admin only)`,
    usage: '{guildprefix}spotify-sync-all',
    cooldown: 10000,
    run: async (client, message, args) => {
        try {
            // Check if user is bot owner
            if (message.author.id !== config.bot.ownerId) {
                return embeds.deny(message, `This command is restricted to the bot owner.`);
            }

            // Show loading message
            const loadingMsg = await embeds.info(message, `🔄 Starting full Spotify token sync from web app...\n\nThis may take a while depending on the number of users.`);

            // Get sync service
            const spotifySync = require('../../utils/spotify-sync');
            const status = spotifySync.getStatus();

            if (!status.hasWebAppConnection) {
                await loadingMsg.edit({
                    embeds: [embeds.deny(message, `❌ Web app database connection not available\n\nCheck your \`SPOTIFY_WEB_MONGODB_URI\` environment variable.`, false)]
                });
                return;
            }

            // Trigger full sync
            const startTime = Date.now();
            await spotifySync.syncAllTokens();
            const endTime = Date.now();
            const duration = endTime - startTime;

            await loadingMsg.edit({
                embeds: [embeds.success(message, `✅ Full Spotify token sync completed!\n\n⏱️ Duration: ${duration}ms\n\nAll tokens from the web app have been synced to the Discord bot database.`, false)]
            });

        } catch (error) {
            console.error('Spotify sync all command error:', error);
            return embeds.deny(message, `Failed to sync all Spotify tokens: ${error.message}`);
        }
    }
};
