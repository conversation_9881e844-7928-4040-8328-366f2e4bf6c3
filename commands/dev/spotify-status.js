const { embeds } = require('../../core/embeds');
const { EmbedBuilder } = require('discord.js');
const { colors } = require('../../config/setup');

module.exports = {
    name: "spotify-status",
    aliases: ['spotifystatus', 'spotify-sync-status'],
    description: `Check the status of the Spotify sync service`,
    usage: '{guildprefix}spotify-status',
    cooldown: 5000,
    run: async (client, message, args) => {
        try {
            // Get sync service status
            const spotifySync = require('../../utils/spotify-sync');
            const status = spotifySync.getStatus();

            // Get some basic stats
            const userCache = require('../../database/cache/models/user');
            
            const statusEmbed = new EmbedBuilder()
                .setColor(status.isRunning ? colors.success : colors.error)
                .setTitle('🎵 Spotify Sync Service Status')
                .setDescription(status.isRunning ? '✅ Service is running' : '❌ Service is not running')
                .addFields([
                    {
                        name: '🔗 Web App Connection',
                        value: status.hasWebAppConnection ? '✅ Connected' : '❌ Not connected',
                        inline: true
                    },
                    {
                        name: '⏰ Last Sync',
                        value: status.lastSyncTime ? `<t:${Math.floor(status.lastSyncTime.getTime() / 1000)}:R>` : 'Never',
                        inline: true
                    },
                    {
                        name: '🔄 Sync Interval',
                        value: status.isRunning ? '30 seconds' : 'N/A',
                        inline: true
                    }
                ])
                .setTimestamp();

            // Add environment variables status
            const envStatus = [];
            if (process.env.SPOTIFY_WEB_MONGODB_URI) {
                envStatus.push('✅ SPOTIFY_WEB_MONGODB_URI');
            } else {
                envStatus.push('❌ SPOTIFY_WEB_MONGODB_URI');
            }
            
            if (process.env.SPOTIFY_WEB_URL) {
                envStatus.push('✅ SPOTIFY_WEB_URL');
            } else {
                envStatus.push('❌ SPOTIFY_WEB_URL');
            }

            if (process.env.SPOTIFY_CLIENT_ID) {
                envStatus.push('✅ SPOTIFY_CLIENT_ID');
            } else {
                envStatus.push('❌ SPOTIFY_CLIENT_ID');
            }

            if (process.env.SPOTIFY_CLIENT_SECRET) {
                envStatus.push('✅ SPOTIFY_CLIENT_SECRET');
            } else {
                envStatus.push('❌ SPOTIFY_CLIENT_SECRET');
            }

            statusEmbed.addFields([
                {
                    name: '⚙️ Environment Variables',
                    value: envStatus.join('\n'),
                    inline: false
                }
            ]);

            await message.channel.send({ embeds: [statusEmbed] });

        } catch (error) {
            console.error('Spotify status command error:', error);
            return embeds.deny(message, `Failed to get Spotify sync status: ${error.message}`);
        }
    }
};
