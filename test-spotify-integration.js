#!/usr/bin/env node

/**
 * Test script for Spotify integration
 * This script tests the connection between the web app and Discord bot
 */

require('dotenv').config();

async function testSpotifyIntegration() {
    console.log('🧪 Testing Spotify Integration...\n');

    // Test 1: Environment Variables
    console.log('1️⃣ Checking Environment Variables:');
    const requiredEnvVars = [
        'SPOTIFY_CLIENT_ID',
        'SPOTIFY_CLIENT_SECRET', 
        'SPOTIFY_REDIRECT_URI',
        'MONGODB_CONNECTION',
        'SPOTIFY_WEB_MONGODB_URI',
        'SPOTIFY_WEB_URL'
    ];

    let envVarsOk = true;
    for (const envVar of requiredEnvVars) {
        if (process.env[envVar]) {
            console.log(`   ✅ ${envVar}`);
        } else {
            console.log(`   ❌ ${envVar} - MISSING`);
            envVarsOk = false;
        }
    }

    if (!envVarsOk) {
        console.log('\n❌ Some environment variables are missing. Please check your .env file.');
        return;
    }

    // Test 2: MongoDB Connection (Web App Database)
    console.log('\n2️⃣ Testing Web App Database Connection:');
    try {
        const { MongoClient } = require('mongodb');
        const client = new MongoClient(process.env.SPOTIFY_WEB_MONGODB_URI);
        await client.connect();
        const db = client.db();
        
        // Test if we can access the spotify_tokens collection
        const collection = db.collection('spotify_tokens');
        const count = await collection.countDocuments();
        
        console.log(`   ✅ Connected to web app database`);
        console.log(`   📊 Found ${count} existing Spotify tokens`);
        
        await client.close();
    } catch (error) {
        console.log(`   ❌ Web app database connection failed: ${error.message}`);
        return;
    }

    // Test 3: Discord Bot Database Connection
    console.log('\n3️⃣ Testing Discord Bot Database Connection:');
    try {
        const mongoose = require('mongoose');
        await mongoose.connect(process.env.MONGODB_CONNECTION);
        
        const { User } = require('./database');
        const userCount = await User.countDocuments({ 'Spotify.accessToken': { $exists: true, $ne: null } });
        
        console.log(`   ✅ Connected to Discord bot database`);
        console.log(`   📊 Found ${userCount} users with Spotify data`);
        
        await mongoose.disconnect();
    } catch (error) {
        console.log(`   ❌ Discord bot database connection failed: ${error.message}`);
        return;
    }

    // Test 4: Spotify API Credentials
    console.log('\n4️⃣ Testing Spotify API Credentials:');
    try {
        const axios = require('axios');
        const response = await axios.post('https://accounts.spotify.com/api/token', 
            new URLSearchParams({
                grant_type: 'client_credentials'
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': `Basic ${Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64')}`
                }
            }
        );

        if (response.data.access_token) {
            console.log(`   ✅ Spotify API credentials are valid`);
            console.log(`   🔑 Successfully obtained client credentials token`);
        } else {
            console.log(`   ❌ Spotify API credentials test failed`);
        }
    } catch (error) {
        console.log(`   ❌ Spotify API credentials test failed: ${error.message}`);
        return;
    }

    // Test 5: Sync Service Initialization
    console.log('\n5️⃣ Testing Sync Service:');
    try {
        const spotifySync = require('./utils/spotify-sync');
        await spotifySync.start();
        
        const status = spotifySync.getStatus();
        console.log(`   ✅ Sync service started successfully`);
        console.log(`   🔗 Web app connection: ${status.hasWebAppConnection ? 'Connected' : 'Failed'}`);
        console.log(`   ⏰ Last sync time: ${status.lastSyncTime}`);
        
        await spotifySync.stop();
        console.log(`   ✅ Sync service stopped successfully`);
    } catch (error) {
        console.log(`   ❌ Sync service test failed: ${error.message}`);
        return;
    }

    // Test 6: Spotify Utility Functions
    console.log('\n6️⃣ Testing Spotify Utility Functions:');
    try {
        const spotifyAPI = require('./utils/spotify');
        console.log(`   ✅ Spotify utility loaded successfully`);
        console.log(`   🔧 Auto-refresh and sync functions available`);
    } catch (error) {
        console.log(`   ❌ Spotify utility test failed: ${error.message}`);
        return;
    }

    console.log('\n🎉 All tests passed! Spotify integration is ready to use.');
    console.log('\n📋 Next steps:');
    console.log('   1. Start the web app: cd spotify-web && npm start');
    console.log('   2. Start the Discord bot: npm start');
    console.log('   3. Test with: ,spotify login in Discord');
}

// Run the test
testSpotifyIntegration().catch(error => {
    console.error('\n💥 Test failed with error:', error);
    process.exit(1);
});
