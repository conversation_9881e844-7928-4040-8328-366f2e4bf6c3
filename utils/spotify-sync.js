const { MongoClient } = require('mongodb');
const spotifyAPI = require('./spotify');

/**
 * Spotify Web App Sync Service
 * Monitors the web app's token database and syncs changes to the Discord bot
 */
class SpotifySyncService {
    constructor() {
        this.isRunning = false;
        this.syncInterval = null;
        this.webAppClient = null;
        this.webAppDB = null;
        this.lastSyncTime = new Date();
    }

    /**
     * Start the sync service
     */
    async start() {
        if (this.isRunning) {
            console.log('⚠️ Spotify sync service is already running');
            return;
        }

        try {
            // Connect to web app database
            if (!process.env.SPOTIFY_WEB_MONGODB_URI) {
                console.log('⚠️ SPOTIFY_WEB_MONGODB_URI not set, sync service disabled');
                return;
            }

            this.webAppClient = new MongoClient(process.env.SPOTIFY_WEB_MONGODB_URI);
            await this.webAppClient.connect();
            this.webAppDB = this.webAppClient.db();

            console.log('✅ Spotify sync service connected to web app database');

            // Start periodic sync (every 30 seconds)
            this.syncInterval = setInterval(() => {
                this.syncRecentChanges().catch(error => {
                    console.error('❌ Spotify sync error:', error);
                });
            }, 30000);

            this.isRunning = true;
            console.log('🔄 Spotify sync service started (checking every 30 seconds)');

            // Do an initial sync
            await this.syncRecentChanges();

        } catch (error) {
            console.error('❌ Failed to start Spotify sync service:', error);
        }
    }

    /**
     * Stop the sync service
     */
    async stop() {
        if (!this.isRunning) return;

        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }

        if (this.webAppClient) {
            await this.webAppClient.close();
            this.webAppClient = null;
            this.webAppDB = null;
        }

        this.isRunning = false;
        console.log('🛑 Spotify sync service stopped');
    }

    /**
     * Sync recent changes from web app database
     */
    async syncRecentChanges() {
        if (!this.webAppDB) return;

        try {
            const collection = this.webAppDB.collection('spotify_tokens');
            
            // Find tokens updated since last sync
            const recentTokens = await collection.find({
                updated_at: { $gte: this.lastSyncTime }
            }).toArray();

            if (recentTokens.length > 0) {
                console.log(`🔄 Found ${recentTokens.length} updated Spotify tokens to sync`);

                for (const tokenData of recentTokens) {
                    try {
                        await this.syncUserTokens(tokenData);
                    } catch (error) {
                        console.error(`❌ Failed to sync tokens for user ${tokenData.discord_user_id}:`, error);
                    }
                }
            }

            this.lastSyncTime = new Date();

        } catch (error) {
            console.error('❌ Error during sync process:', error);
        }
    }

    /**
     * Sync individual user tokens
     * @param {Object} tokenData - Token data from web app database
     */
    async syncUserTokens(tokenData) {
        const userId = tokenData.discord_user_id;

        // Get user profile from Spotify to update additional data
        let profileData = {};
        try {
            const axios = require('axios');
            const response = await axios.get('https://api.spotify.com/v1/me', {
                headers: { 'Authorization': `Bearer ${tokenData.access_token}` }
            });
            profileData = {
                spotifyId: response.data.id,
                displayName: response.data.display_name,
                email: response.data.email,
                country: response.data.country,
                product: response.data.product
            };
        } catch (error) {
            // Profile fetch failed, continue with token sync only
            console.log(`⚠️ Could not fetch profile for user ${userId}:`, error.message);
        }

        // Update Discord bot database
        const userCache = require('../database/cache/models/user');
        const spotifyData = {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiry: new Date(tokenData.expires_at),
            connectedAt: tokenData.created_at || new Date(),
            ...profileData
        };

        await userCache.setSpotifyData(userId, spotifyData);
        console.log(`✅ Synced Spotify tokens for user ${userId}`);
    }

    /**
     * Force sync all tokens (useful for initial setup)
     */
    async syncAllTokens() {
        if (!this.webAppDB) {
            console.log('⚠️ Web app database not connected');
            return;
        }

        try {
            const collection = this.webAppDB.collection('spotify_tokens');
            const allTokens = await collection.find({}).toArray();

            console.log(`🔄 Starting full sync of ${allTokens.length} Spotify tokens`);

            for (const tokenData of allTokens) {
                try {
                    await this.syncUserTokens(tokenData);
                } catch (error) {
                    console.error(`❌ Failed to sync tokens for user ${tokenData.discord_user_id}:`, error);
                }
            }

            console.log('✅ Full Spotify token sync completed');

        } catch (error) {
            console.error('❌ Error during full sync:', error);
        }
    }

    /**
     * Get sync service status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastSyncTime: this.lastSyncTime,
            hasWebAppConnection: !!this.webAppDB
        };
    }
}

// Export singleton instance
module.exports = new SpotifySyncService();
