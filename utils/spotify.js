const axios = require('axios');
const config = require('../config/setup');
const { User } = require('../database');
const userCache = require('../database/cache/models/user');
const { MongoClient } = require('mongodb');

/**
 * Spotify Web API utility functions
 */
class SpotifyAPI {
    constructor() {
        this.clientId = config.apis.spotify.clientId;
        this.clientSecret = config.apis.spotify.clientSecret;
        this.redirectUri = config.apis.spotify.redirectUri;
        this.baseURL = 'https://api.spotify.com/v1';
        this.authURL = 'https://accounts.spotify.com';
        this.webAppDB = null;
        this.initializeWebAppConnection();
    }

    /**
     * Initialize connection to the web app's MongoDB database
     */
    async initializeWebAppConnection() {
        try {
            if (!process.env.SPOTIFY_WEB_MONGODB_URI) {
                console.log('⚠️ SPOTIFY_WEB_MONGODB_URI not set, web app sync disabled');
                return;
            }

            this.webAppClient = new MongoClient(process.env.SPOTIFY_WEB_MONGODB_URI);
            await this.webAppClient.connect();
            this.webAppDB = this.webAppClient.db();
            console.log('✅ Connected to Spotify web app database for token sync');
        } catch (error) {
            console.error('❌ Failed to connect to Spotify web app database:', error);
        }
    }



    /**
     * Sync tokens from web app database to Discord bot database
     * @param {string} userId - Discord user ID
     * @returns {boolean} Success status
     */
    async syncTokensFromWebApp(userId) {
        if (!this.webAppDB) return false;

        try {
            const collection = this.webAppDB.collection('spotify_tokens');
            const webAppTokens = await collection.findOne({ discord_user_id: userId });

            if (!webAppTokens) return false;

            // Get user profile from Spotify to update additional data
            let profileData = {};
            try {
                const response = await axios.get('https://api.spotify.com/v1/me', {
                    headers: { 'Authorization': `Bearer ${webAppTokens.access_token}` }
                });
                profileData = {
                    spotifyId: response.data.id,
                    displayName: response.data.display_name,
                    email: response.data.email,
                    country: response.data.country,
                    product: response.data.product
                };
            } catch (error) {
                console.log('Could not fetch profile data during sync:', error.message);
            }

            // Update Discord bot database
            const spotifyData = {
                accessToken: webAppTokens.access_token,
                refreshToken: webAppTokens.refresh_token,
                tokenExpiry: new Date(webAppTokens.expires_at),
                connectedAt: webAppTokens.created_at || new Date(),
                ...profileData
            };

            await userCache.setSpotifyData(userId, spotifyData);
            console.log(`🔄 Synced Spotify tokens for user ${userId} from web app`);
            return true;
        } catch (error) {
            console.error('Error syncing tokens from web app:', error);
            return false;
        }
    }

    /**
     * Refresh access token using web app's refresh logic
     * @param {string} userId - Discord user ID
     * @returns {string|null} New access token or null if failed
     */
    async refreshAccessToken(userId) {
        try {
            const spotifyData = await userCache.getSpotifyData(userId);
            if (!spotifyData?.refreshToken) {
                return null;
            }

            const response = await axios.post('https://accounts.spotify.com/api/token',
                new URLSearchParams({
                    grant_type: 'refresh_token',
                    refresh_token: spotifyData.refreshToken
                }), {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Authorization': `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`
                    }
                }
            );

            const tokenData = response.data;
            const newExpiry = new Date(Date.now() + tokenData.expires_in * 1000);

            // Update Discord bot database
            const updatedSpotifyData = {
                ...spotifyData,
                accessToken: tokenData.access_token,
                tokenExpiry: newExpiry
            };
            await userCache.setSpotifyData(userId, updatedSpotifyData);

            // Update web app database if connected
            if (this.webAppDB) {
                try {
                    const collection = this.webAppDB.collection('spotify_tokens');
                    await collection.updateOne(
                        { discord_user_id: userId },
                        {
                            $set: {
                                access_token: tokenData.access_token,
                                expires_at: newExpiry.getTime(),
                                updated_at: new Date()
                            }
                        }
                    );
                } catch (error) {
                    console.error('Failed to update web app database during refresh:', error);
                }
            }

            console.log(`🔄 Refreshed Spotify token for user ${userId}`);
            return tokenData.access_token;
        } catch (error) {
            console.error('Error refreshing access token:', error);
            return null;
        }
    }

    /**
     * Get valid access token for user (with auto-refresh and web app sync)
     * @param {string} userId - Discord user ID
     * @returns {string|null} Valid access token or null if not authenticated/expired
     */
    async getValidToken(userId) {
        try {
            let spotifyData = await userCache.getSpotifyData(userId);

            // If no local data, try to sync from web app
            if (!spotifyData?.accessToken) {
                const synced = await this.syncTokensFromWebApp(userId);
                if (synced) {
                    spotifyData = await userCache.getSpotifyData(userId);
                } else {
                    return null;
                }
            }

            // Check if token is expired (with 5 minute buffer)
            const now = Date.now();
            const expiry = new Date(spotifyData.tokenExpiry).getTime();
            const bufferTime = 5 * 60 * 1000; // 5 minutes

            if (now >= expiry - bufferTime) {
                // Token is expired or about to expire, try to refresh
                const newToken = await this.refreshAccessToken(userId);
                if (newToken) {
                    return newToken;
                } else {
                    // Refresh failed, try syncing from web app as fallback
                    const synced = await this.syncTokensFromWebApp(userId);
                    if (synced) {
                        const updatedData = await userCache.getSpotifyData(userId);
                        return updatedData.accessToken;
                    }
                    return null;
                }
            }

            return spotifyData.accessToken;
        } catch (error) {
            console.error('Error getting valid token:', error);
            return null;
        }
    }

    /**
     * Make authenticated request to Spotify API
     * @param {string} userId - Discord user ID
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {Object} data - Request data
     * @returns {Object} API response
     */
    async makeRequest(userId, endpoint, method = 'GET', data = null) {
        const token = await this.getValidToken(userId);
        if (!token) {
            throw new Error('User not authenticated with Spotify');
        }

        try {
            const config = {
                method: method,
                url: `${this.baseURL}${endpoint}`,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            };

            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            if (error.response?.status === 401) {
                // Token is invalid or expired, user needs to re-authenticate
                throw new Error('Spotify token expired or invalid. Please run `spotify login` again to get a new token.');
            }
            throw new Error(`Spotify API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    /**
     * Get user's Spotify profile
     * @param {string} userId - Discord user ID
     * @returns {Object} User profile data
     */
    async getUserProfile(userId) {
        return await this.makeRequest(userId, '/me');
    }

    /**
     * Get user's current playback state
     * @param {string} userId - Discord user ID
     * @returns {Object|null} Playback state or null if nothing playing
     */
    async getCurrentPlayback(userId) {
        try {
            return await this.makeRequest(userId, '/me/player');
        } catch (error) {
            if (error.message.includes('No active device')) {
                return null;
            }
            throw error;
        }
    }

    /**
     * Get user's available devices
     * @param {string} userId - Discord user ID
     * @returns {Array} List of available devices
     */
    async getDevices(userId) {
        const response = await this.makeRequest(userId, '/me/player/devices');
        return response.devices || [];
    }

    /**
     * Search for tracks, artists, albums, or playlists
     * @param {string} userId - Discord user ID
     * @param {string} query - Search query
     * @param {string} type - Search type (track, artist, album, playlist)
     * @param {number} limit - Number of results to return
     * @returns {Object} Search results
     */
    async search(userId, query, type = 'track', limit = 20) {
        const endpoint = `/search?q=${encodeURIComponent(query)}&type=${type}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Get user's top tracks or artists
     * @param {string} userId - Discord user ID
     * @param {string} type - 'tracks' or 'artists'
     * @param {string} timeRange - 'short_term', 'medium_term', or 'long_term'
     * @param {number} limit - Number of results to return
     * @returns {Object} Top items
     */
    async getTopItems(userId, type, timeRange = 'medium_term', limit = 20) {
        const endpoint = `/me/top/${type}?time_range=${timeRange}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Format duration from milliseconds to MM:SS
     * @param {number} ms - Duration in milliseconds
     * @returns {string} Formatted duration
     */
    formatDuration(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * Format time range for display
     * @param {string} timeRange - Spotify time range
     * @returns {string} Human readable time range
     */
    formatTimeRange(timeRange) {
        const ranges = {
            'short_term': 'Last 4 weeks',
            'medium_term': 'Last 6 months',
            'long_term': 'All time'
        };
        return ranges[timeRange] || timeRange;
    }
}

module.exports = new SpotifyAPI();
