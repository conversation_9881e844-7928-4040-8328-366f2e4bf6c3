const axios = require('axios');
const config = require('../config/setup');
const { User } = require('../database');
const userCache = require('../database/cache/models/user');

/**
 * Spotify Web API utility functions
 */
class SpotifyAPI {
    constructor() {
        this.clientId = config.apis.spotify.clientId;
        this.clientSecret = config.apis.spotify.clientSecret;
        this.redirectUri = config.apis.spotify.redirectUri;
        this.baseURL = 'https://api.spotify.com/v1';
        this.authURL = 'https://accounts.spotify.com';
    }



    /**
     * Get valid access token for user (check expiration)
     * @param {string} userId - Discord user ID
     * @returns {string|null} Valid access token or null if not authenticated/expired
     */
    async getValidToken(userId) {
        try {
            const spotifyData = await userCache.getSpotifyData(userId);
            if (!spotifyData?.accessToken) {
                return null;
            }

            // Check if token is expired
            const now = new Date();
            const expiry = new Date(spotifyData.tokenExpiry);

            if (now >= expiry) {
                // Token is expired, user needs to login again
                return null;
            }

            return spotifyData.accessToken;
        } catch (error) {
            console.error('Error getting valid token:', error);
            return null;
        }
    }

    /**
     * Make authenticated request to Spotify API
     * @param {string} userId - Discord user ID
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {Object} data - Request data
     * @returns {Object} API response
     */
    async makeRequest(userId, endpoint, method = 'GET', data = null) {
        const token = await this.getValidToken(userId);
        if (!token) {
            throw new Error('User not authenticated with Spotify');
        }

        try {
            const config = {
                method: method,
                url: `${this.baseURL}${endpoint}`,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            };

            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            if (error.response?.status === 401) {
                // Token is invalid or expired, user needs to re-authenticate
                throw new Error('Spotify token expired or invalid. Please run `spotify login` again to get a new token.');
            }
            throw new Error(`Spotify API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    /**
     * Get user's Spotify profile
     * @param {string} userId - Discord user ID
     * @returns {Object} User profile data
     */
    async getUserProfile(userId) {
        return await this.makeRequest(userId, '/me');
    }

    /**
     * Get user's current playback state
     * @param {string} userId - Discord user ID
     * @returns {Object|null} Playback state or null if nothing playing
     */
    async getCurrentPlayback(userId) {
        try {
            return await this.makeRequest(userId, '/me/player');
        } catch (error) {
            if (error.message.includes('No active device')) {
                return null;
            }
            throw error;
        }
    }

    /**
     * Get user's available devices
     * @param {string} userId - Discord user ID
     * @returns {Array} List of available devices
     */
    async getDevices(userId) {
        const response = await this.makeRequest(userId, '/me/player/devices');
        return response.devices || [];
    }

    /**
     * Search for tracks, artists, albums, or playlists
     * @param {string} userId - Discord user ID
     * @param {string} query - Search query
     * @param {string} type - Search type (track, artist, album, playlist)
     * @param {number} limit - Number of results to return
     * @returns {Object} Search results
     */
    async search(userId, query, type = 'track', limit = 20) {
        const endpoint = `/search?q=${encodeURIComponent(query)}&type=${type}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Get user's top tracks or artists
     * @param {string} userId - Discord user ID
     * @param {string} type - 'tracks' or 'artists'
     * @param {string} timeRange - 'short_term', 'medium_term', or 'long_term'
     * @param {number} limit - Number of results to return
     * @returns {Object} Top items
     */
    async getTopItems(userId, type, timeRange = 'medium_term', limit = 20) {
        const endpoint = `/me/top/${type}?time_range=${timeRange}&limit=${limit}`;
        return await this.makeRequest(userId, endpoint);
    }

    /**
     * Format duration from milliseconds to MM:SS
     * @param {number} ms - Duration in milliseconds
     * @returns {string} Formatted duration
     */
    formatDuration(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * Format time range for display
     * @param {string} timeRange - Spotify time range
     * @returns {string} Human readable time range
     */
    formatTimeRange(timeRange) {
        const ranges = {
            'short_term': 'Last 4 weeks',
            'medium_term': 'Last 6 months',
            'long_term': 'All time'
        };
        return ranges[timeRange] || timeRange;
    }
}

module.exports = new SpotifyAPI();
