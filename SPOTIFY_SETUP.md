# Spotify Integration Setup

This document explains how to set up the integrated Spotify system that automatically syncs tokens between the web app and Discord bot.

## Overview

The Spotify integration consists of two parts:
1. **Spotify Web App** (`spotify-web/`) - Handles OAuth authentication and stores tokens
2. **Discord Bot** - Uses tokens for Spotify commands and automatically syncs with web app

## Environment Variables

Add these environment variables to your `.env` file:

### Required for Web App
```env
# Spotify API credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
SPOTIFY_REDIRECT_URI=http://your-server.com:3000/callback

# Web app database (can be same as bot database)
MONGODB_URI=mongodb://localhost:27017/adore

# Web app server settings
PORT=3000
```

### Required for Discord Bot
```env
# Same Spotify credentials as web app
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
SPOTIFY_REDIRECT_URI=http://your-server.com:3000/callback

# Web app database connection for token sync
SPOTIFY_WEB_MONGODB_URI=mongodb://localhost:27017/adore

# Web app URL for login redirects
SPOTIFY_WEB_URL=http://your-server.com:3000
```

## Setup Steps

### 1. Spotify App Configuration

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app or use existing one
3. Add redirect URI: `http://your-server.com:3000/callback`
4. Copy Client ID and Client Secret to your `.env` file

### 2. Start the Web App

```bash
cd spotify-web
npm install
npm start
```

The web app will run on port 3000 by default.

### 3. Start the Discord Bot

The bot will automatically:
- Connect to the web app's database
- Start the token sync service
- Monitor for new authentications

```bash
npm start
```

## How It Works

### Authentication Flow

1. User runs `,spotify login` in Discord
2. Bot sends a link to the web app: `http://your-server.com:3000/login?discordId=USER_ID`
3. User clicks link and authorizes with Spotify
4. Web app stores tokens in `spotify_tokens` collection
5. Bot automatically syncs tokens within 30 seconds
6. User can immediately use Spotify commands

### Automatic Token Refresh

- Bot checks token expiration before each API call
- If token is expired, bot automatically refreshes it
- Refreshed tokens are synced back to web app database
- No user intervention required

### Manual Sync

Users can manually sync tokens using:
```
,spotify sync
```

This is useful if the automatic sync fails or for immediate synchronization.

## Database Collections

### `spotify_tokens` (Web App)
```javascript
{
  discord_user_id: "*********",
  access_token: "BQA...",
  refresh_token: "AQA...",
  expires_at: *************,
  created_at: Date,
  updated_at: Date
}
```

### `users.Spotify` (Discord Bot)
```javascript
{
  accessToken: "BQA...",
  refreshToken: "AQA...",
  tokenExpiry: Date,
  spotifyId: "spotify_user_id",
  displayName: "User Name",
  email: "<EMAIL>",
  country: "US",
  product: "premium",
  connectedAt: Date
}
```

## Commands

### Authentication
- `,spotify login` - Connect Spotify account via web app
- `,spotify logout` - Disconnect Spotify account
- `,spotify sync` - Manually sync tokens from web app

### Playback Control
- `,spotify` - Show current playing track
- `,spotify play [song]` - Search and play a song
- `,spotify pause` - Pause playback
- `,spotify resume` - Resume playback
- `,spotify next` - Skip to next song
- `,spotify previous` - Go to previous song

### Additional Features
- Automatic token refresh
- Device management
- Queue control
- Volume control
- Library management (like/unlike)
- Statistics (top tracks/artists)

## Troubleshooting

### Bot can't connect to web app database
- Check `SPOTIFY_WEB_MONGODB_URI` environment variable
- Ensure web app database is accessible from bot server
- Check MongoDB connection string format

### Tokens not syncing
- Check if sync service started successfully in bot logs
- Verify both bot and web app use same database
- Try manual sync with `,spotify sync`

### Authentication fails
- Verify Spotify app redirect URI matches web app URL
- Check Spotify Client ID and Secret are correct
- Ensure web app is accessible at the configured URL

## Logs

The system provides detailed logging:

### Bot Startup
```
✅ Spotify sync service connected to web app database
🔄 Spotify sync service started (checking every 30 seconds)
```

### Token Sync
```
🔄 Found 1 updated Spotify tokens to sync
✅ Synced Spotify tokens for user *********
```

### Token Refresh
```
🔄 Refreshed Spotify token for user *********
```
