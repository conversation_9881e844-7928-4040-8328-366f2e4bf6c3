const BaseCache = require('./base');

class System {
  constructor() {
    // Automatic cache for system data
    this.cache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour TTL
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'System', // Database model name
      keyField: '_id', // Primary key field
      fieldsToCache: ['Blacklist'] // Fields to cache
    });
  }

  // Get user blacklist - automatic cache handling
  async getUserBlacklist(userId) {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.users) return false;
    
    return system.Blacklist.users.some(entry => entry.userId === userId);
  }

  // Get server blacklist - automatic cache handling
  async getServerBlacklist(serverId) {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.servers) return false;
    
    return system.Blacklist.servers.some(entry => entry.serverId === serverId);
  }

  // Add user to blacklist - automatic database and cache update
  async addUserBlacklist(userId, reason, blacklistedBy) {
    const system = await this.cache.get('system') || { _id: 'system', Blacklist: { users: [], servers: [] } };
    
    // Remove existing entry if any
    if (system.Blacklist && system.Blacklist.users) {
      system.Blacklist.users = system.Blacklist.users.filter(entry => entry.userId !== userId);
    } else {
      system.Blacklist = { users: [], servers: [] };
    }
    
    // Add new entry
    system.Blacklist.users.push({
      userId,
      reason,
      blacklistedBy,
      timestamp: new Date()
    });
    
    return await this.cache.set('system', system);
  }

  // Add server to blacklist - automatic database and cache update
  async addServerBlacklist(serverId, reason, blacklistedBy) {
    const system = await this.cache.get('system') || { _id: 'system', Blacklist: { users: [], servers: [] } };
    
    // Remove existing entry if any
    if (system.Blacklist && system.Blacklist.servers) {
      system.Blacklist.servers = system.Blacklist.servers.filter(entry => entry.serverId !== serverId);
    } else {
      system.Blacklist = { users: [], servers: [] };
    }
    
    // Add new entry
    system.Blacklist.servers.push({
      serverId,
      reason,
      blacklistedBy,
      timestamp: new Date()
    });
    
    return await this.cache.set('system', system);
  }

  // Remove user from blacklist - automatic database and cache update
  async removeUserBlacklist(userId) {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.users) return false;
    
    system.Blacklist.users = system.Blacklist.users.filter(entry => entry.userId !== userId);
    
    return await this.cache.set('system', system);
  }

  // Remove server from blacklist - automatic database and cache update
  async removeServerBlacklist(serverId) {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.servers) return false;
    
    system.Blacklist.servers = system.Blacklist.servers.filter(entry => entry.serverId !== serverId);
    
    return await this.cache.set('system', system);
  }

  // Get all blacklisted users - automatic cache handling
  async getAllBlacklistedUsers() {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.users) return [];
    
    return system.Blacklist.users;
  }

  // Get all blacklisted servers - automatic cache handling
  async getAllBlacklistedServers() {
    const system = await this.cache.get('system');
    if (!system || !system.Blacklist || !system.Blacklist.servers) return [];
    
    return system.Blacklist.servers;
  }

  // Utility methods
  invalidateSystem() {
    this.cache.invalidate('system');
  }

  clearCache() {
    this.cache.clear();
  }

  getStats() {
    return {
      system: this.cache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new System();
