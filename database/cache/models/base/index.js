/**
 * Base Cache Class
 * Provides a foundation for all cache implementations with TTL support
 */
class BaseCache {
  /**
   * Create a new cache instance
   * @param {Object} options - Cache configuration options
   * @param {number} options.ttl - Time to live for active entries in milliseconds
   * @param {number} options.expiredTtl - Time to keep expired entries before removal in milliseconds
   * @param {string} options.modelName - Database model name
   * @param {string} options.keyField - Primary key field in the model
   * @param {Array<string>} options.fieldsToCache - Fields to cache from the model
   */
  constructor(options = {}) {
    this.ttl = options.ttl || 3600000; // Default 1 hour
    this.expiredTtl = options.expiredTtl || 86400000; // Default 24 hours
    this.modelName = options.modelName;
    this.keyField = options.keyField;
    this.fieldsToCache = options.fieldsToCache || [];
    
    // Initialize cache Map
    this.cache = new Map();
    
    // Set up automatic cleanup
    this.setupCleanupInterval();
  }

  /**
   * Set up interval for cache cleanup
   */
  setupCleanupInterval() {
    // Run cleanup every 15 minutes
    this.cleanupInterval = setInterval(() => {
      this.moveActiveToExpired();
      this.cleanupExpired();
    }, 900000);
  }

  /**
   * Get an item from cache, or fetch from database if not cached
   * @param {string} key - Cache key
   * @returns {Promise<Object>} Cached item
   */
  async get(key) {
    // Check if item exists in cache and is not expired
    const cachedItem = this.cache.get(key);
    
    if (cachedItem && cachedItem.status === 'active') {
      return cachedItem.data;
    }
    
    // If not in cache or expired, fetch from database
    try {
      const mongoose = require('mongoose');
      const Model = mongoose.model(this.modelName);
      
      // Create query object
      const query = {};
      query[this.keyField] = key;
      
      // Fetch from database
      const item = await Model.findOne(query);
      
      if (item) {
        // Store in cache
        this.cache.set(key, {
          data: item,
          timestamp: Date.now(),
          status: 'active'
        });
        
        return item;
      }
      
      return null;
    } catch (error) {
      console.error(`Error fetching ${this.modelName} with ${this.keyField}=${key}:`, error);
      
      // If there was an error but we have an expired version, return that
      if (cachedItem && cachedItem.status === 'expired') {
        return cachedItem.data;
      }
      
      return null;
    }
  }

  /**
   * Get a specific field from a cached item
   * @param {string} key - Cache key
   * @param {string} field - Field name
   * @returns {Promise<any>} Field value
   */
  async getField(key, field) {
    const item = await this.get(key);
    return item ? item[field] : null;
  }

  /**
   * Set an item in cache and update database
   * @param {string} key - Cache key
   * @param {Object} data - Data to cache
   * @returns {Promise<boolean>} Success status
   */
  async set(key, data) {
    try {
      const mongoose = require('mongoose');
      const Model = mongoose.model(this.modelName);
      
      // Create query object
      const query = {};
      query[this.keyField] = key;
      
      // Update or create in database
      await Model.findOneAndUpdate(
        query,
        data,
        { upsert: true, new: true, setDefaultsOnInsert: true }
      );
      
      // Update cache
      this.cache.set(key, {
        data,
        timestamp: Date.now(),
        status: 'active'
      });
      
      return true;
    } catch (error) {
      console.error(`Error setting ${this.modelName} with ${this.keyField}=${key}:`, error);
      return false;
    }
  }

  /**
   * Set a specific field in a cached item and update database
   * @param {string} key - Cache key
   * @param {string} field - Field name
   * @param {any} value - Field value
   * @returns {Promise<boolean>} Success status
   */
  async setField(key, field, value) {
    try {
      const mongoose = require('mongoose');
      const Model = mongoose.model(this.modelName);
      
      // Create query and update objects
      const query = {};
      query[this.keyField] = key;
      
      const update = {};
      update[field] = value;
      
      // Update or create in database
      const item = await Model.findOneAndUpdate(
        query,
        { $set: update },
        { upsert: true, new: true, setDefaultsOnInsert: true }
      );
      
      // Update cache if we have the item cached
      const cachedItem = this.cache.get(key);
      if (cachedItem) {
        cachedItem.data[field] = value;
        cachedItem.timestamp = Date.now();
        cachedItem.status = 'active';
      } else {
        // Store the whole item in cache
        this.cache.set(key, {
          data: item,
          timestamp: Date.now(),
          status: 'active'
        });
      }
      
      return true;
    } catch (error) {
      console.error(`Error setting field ${field} for ${this.modelName} with ${this.keyField}=${key}:`, error);
      return false;
    }
  }

  /**
   * Invalidate a cached item
   * @param {string} key - Cache key
   */
  invalidate(key) {
    this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Move active entries to expired status
   */
  moveActiveToExpired() {
    const now = Date.now();
    
    for (const [key, data] of this.cache.entries()) {
      if (data.status === 'active' && data.timestamp + this.ttl < now) {
        data.status = 'expired';
      }
    }
  }

  /**
   * Remove expired entries completely
   */
  cleanupExpired() {
    const now = Date.now();
    
    for (const [key, data] of this.cache.entries()) {
      if (data.status === 'expired' && data.timestamp + this.expiredTtl < now) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    let activeCount = 0;
    let expiredCount = 0;
    
    for (const data of this.cache.values()) {
      if (data.status === 'active') {
        activeCount++;
      } else {
        expiredCount++;
      }
    }
    
    return {
      total: this.cache.size,
      active: activeCount,
      expired: expiredCount
    };
  }
}

module.exports = BaseCache;
