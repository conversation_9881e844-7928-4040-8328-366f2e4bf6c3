const BaseCache = require('./base');

class Guild {
  constructor() {
    // Automatic cache for guild data
    this.cache = new BaseCache({
      ttl: 60 * 60 * 1000, // 1 hour TTL
      expiredTtl: 24 * 60 * 60 * 1000, // 24 hours expired
      modelName: 'Guild', // Database model name
      keyField: 'Guild<PERSON>', // Primary key field
      fieldsToCache: [
        'Prefix', 'WelcomeChannel', 'WelcomeMessage',
        'LeaveChannel', 'LeaveMessage', 'BoostChannel',
        'BoostMessage', 'BoostRole', 'JailRoleID', 'JailChannelID',
        'Vanity', 'VanityRoles', 'VanityMessage',
        'VanityLogChannel', 'AutoRoles', 'AutoReact',
        'AutoResponder', 'ForceNick', 'AFK', 'AutoModFilter',
        'LastFMCrowns', 'SwearTracker', 'GuildNameHistory', 'BirthdayRole',
        'MessageStats'
      ]
    });
  }

  // Get guild prefix - automatic cache handling
  async getPrefix(guildId) {
    const prefix = await this.cache.getField(guildId, 'Prefix');
    return prefix || ','; // Default prefix if null/undefined
  }

  // Set guild prefix - automatic database and cache update
  async setPrefix(guildId, prefix) {
    return await this.cache.setField(guildId, 'Prefix', prefix);
  }

  // Get welcome channel - automatic cache handling
  async getWelcomeChannel(guildId) {
    return await this.cache.getField(guildId, 'WelcomeChannel');
  }

  // Get welcome message - automatic cache handling
  async getWelcomeMessage(guildId) {
    return await this.cache.getField(guildId, 'WelcomeMessage');
  }

  // Set welcome configuration - automatic database and cache update
  async setWelcome(guildId, channelId, message) {
    await this.cache.setField(guildId, 'WelcomeChannel', channelId);
    return await this.cache.setField(guildId, 'WelcomeMessage', message);
  }

  // Remove welcome configuration
  async removeWelcome(guildId) {
    await this.cache.setField(guildId, 'WelcomeChannel', null);
    return await this.cache.setField(guildId, 'WelcomeMessage', null);
  }

  // Get leave channel - automatic cache handling
  async getLeaveChannel(guildId) {
    return await this.cache.getField(guildId, 'LeaveChannel');
  }

  // Get leave message - automatic cache handling
  async getLeaveMessage(guildId) {
    return await this.cache.getField(guildId, 'LeaveMessage');
  }

  // Set leave configuration - automatic database and cache update
  async setLeave(guildId, channelId, message) {
    await this.cache.setField(guildId, 'LeaveChannel', channelId);
    return await this.cache.setField(guildId, 'LeaveMessage', message);
  }

  // Remove leave configuration
  async removeLeave(guildId) {
    await this.cache.setField(guildId, 'LeaveChannel', null);
    return await this.cache.setField(guildId, 'LeaveMessage', null);
  }

  // Get boost channel - automatic cache handling
  async getBoostChannel(guildId) {
    return await this.cache.getField(guildId, 'BoostChannel');
  }

  // Get boost message - automatic cache handling
  async getBoostMessage(guildId) {
    return await this.cache.getField(guildId, 'BoostMessage');
  }

  // Get boost role - automatic cache handling
  async getBoostRole(guildId) {
    return await this.cache.getField(guildId, 'BoostRole');
  }

  // Set boost configuration - automatic database and cache update
  async setBoost(guildId, channelId, message) {
    await this.cache.setField(guildId, 'BoostChannel', channelId);
    return await this.cache.setField(guildId, 'BoostMessage', message);
  }

  // Set boost role - automatic database and cache update
  async setBoostRole(guildId, roleId) {
    return await this.cache.setField(guildId, 'BoostRole', roleId);
  }

  // Remove boost configuration
  async removeBoost(guildId) {
    await this.cache.setField(guildId, 'BoostChannel', null);
    return await this.cache.setField(guildId, 'BoostMessage', null);
  }

  // Remove boost role
  async removeBoostRole(guildId) {
    return await this.cache.setField(guildId, 'BoostRole', null);
  }

  // Get jail role ID - automatic cache handling
  async getJailRoleID(guildId) {
    return await this.cache.getField(guildId, 'JailRoleID');
  }

  // Get jail channel ID - automatic cache handling
  async getJailChannelID(guildId) {
    return await this.cache.getField(guildId, 'JailChannelID');
  }

  // Set jail configuration - automatic database and cache update
  async setJail(guildId, roleId, channelId) {
    await this.cache.setField(guildId, 'JailRoleID', roleId);
    return await this.cache.setField(guildId, 'JailChannelID', channelId);
  }



  // Get AFK status for a user in a guild - automatic cache handling
  async getAFKStatus(guildId, userId) {
    const afkList = await this.cache.getField(guildId, 'AFK') || [];
    return afkList.find(afk => afk.userId === userId);
  }

  // Set AFK status for a user in a guild - automatic database and cache update
  async setAFKStatus(guildId, userId, message = 'AFK') {
    // Get current AFK list
    let afkList = await this.cache.getField(guildId, 'AFK') || [];
    
    // Remove existing entry if any
    afkList = afkList.filter(afk => afk.userId !== userId);
    
    // Add new entry
    afkList.push({
      userId,
      message,
      since: new Date()
    });
    
    // Update in cache and database
    return await this.cache.setField(guildId, 'AFK', afkList);
  }

  // Remove AFK status for a user in a guild - automatic database and cache update
  async removeAFKStatus(guildId, userId) {
    // Get current AFK list
    let afkList = await this.cache.getField(guildId, 'AFK') || [];

    // Remove user's entry
    afkList = afkList.filter(afk => afk.userId !== userId);

    // Update in cache and database
    return await this.cache.setField(guildId, 'AFK', afkList);
  }

  // ===== FORCE NICK METHODS =====

  // Get forced nickname for a user in a guild - automatic cache handling
  async getForceNick(guildId, userId) {
    const forceNickList = await this.cache.getField(guildId, 'ForceNick') || [];
    return forceNickList.find(fn => fn.userId === userId);
  }

  // Set forced nickname for a user in a guild - automatic database and cache update
  async setForceNick(guildId, userId, nickname, setBy) {
    // Get current ForceNick list
    let forceNickList = await this.cache.getField(guildId, 'ForceNick') || [];

    // Remove existing entry if any
    forceNickList = forceNickList.filter(fn => fn.userId !== userId);

    // Add new entry
    forceNickList.push({
      userId,
      nickname,
      setBy,
      setAt: new Date()
    });

    // Update in cache and database
    return await this.cache.setField(guildId, 'ForceNick', forceNickList);
  }

  // Remove forced nickname for a user in a guild - automatic database and cache update
  async removeForceNick(guildId, userId) {
    // Get current ForceNick list
    let forceNickList = await this.cache.getField(guildId, 'ForceNick') || [];

    // Remove the user's entry
    forceNickList = forceNickList.filter(fn => fn.userId !== userId);

    // Update in cache and database
    return await this.cache.setField(guildId, 'ForceNick', forceNickList);
  }

  // Get all forced nicknames for a guild - automatic cache handling
  async getAllForceNicks(guildId) {
    return await this.cache.getField(guildId, 'ForceNick') || [];
  }

  // Clear all forced nicknames for a guild - automatic database and cache update
  async clearAllForceNicks(guildId) {
    return await this.cache.setField(guildId, 'ForceNick', []);
  }

  // ===== AUTO ROLE METHODS =====

  // Get auto roles for a guild - automatic cache handling
  async getAutoRoles(guildId) {
    return await this.cache.getField(guildId, 'AutoRoles') || [];
  }

  // Add auto role to a guild - automatic database and cache update
  async addAutoRole(guildId, roleId) {
    // Get current auto roles
    let autoRoles = await this.cache.getField(guildId, 'AutoRoles') || [];

    // Add role if not already present
    if (!autoRoles.includes(roleId)) {
      autoRoles.push(roleId);
      return await this.cache.setField(guildId, 'AutoRoles', autoRoles);
    }

    return true;
  }

  // Remove auto role from a guild - automatic database and cache update
  async removeAutoRole(guildId, roleId) {
    // Get current auto roles
    let autoRoles = await this.cache.getField(guildId, 'AutoRoles') || [];

    // Remove the role
    autoRoles = autoRoles.filter(id => id !== roleId);

    // Update in cache and database
    return await this.cache.setField(guildId, 'AutoRoles', autoRoles);
  }

  // Clear all auto roles for a guild - automatic database and cache update
  async clearAllAutoRoles(guildId) {
    return await this.cache.setField(guildId, 'AutoRoles', []);
  }

  // ===== AUTO REACT METHODS =====

  // Get auto reacts for a guild - automatic cache handling
  async getAutoReacts(guildId) {
    return await this.cache.getField(guildId, 'AutoReact') || [];
  }

  // Add auto react to a guild - automatic database and cache update
  async addAutoReact(guildId, word, emojiId, emojiName, isCustom) {
    // Get current auto reacts
    let autoReacts = await this.cache.getField(guildId, 'AutoReact') || [];

    // Add new auto react
    autoReacts.push({
      word: word.toLowerCase(),
      emojiId,
      emojiName,
      isCustom
    });

    // Update in cache and database
    return await this.cache.setField(guildId, 'AutoReact', autoReacts);
  }

  // Remove auto react from a guild - automatic database and cache update
  async removeAutoReact(guildId, word) {
    // Get current auto reacts
    let autoReacts = await this.cache.getField(guildId, 'AutoReact') || [];

    // Remove the auto react
    autoReacts = autoReacts.filter(ar => ar.word !== word.toLowerCase());

    // Update in cache and database
    return await this.cache.setField(guildId, 'AutoReact', autoReacts);
  }

  // Clear all auto reacts for a guild - automatic database and cache update
  async clearAllAutoReacts(guildId) {
    return await this.cache.setField(guildId, 'AutoReact', []);
  }

  // ===== AUTO RESPONDER METHODS =====

  // Get auto responders for a guild - automatic cache handling
  async getAutoResponders(guildId) {
    return await this.cache.getField(guildId, 'AutoResponder') || [];
  }

  // Add auto responder to a guild - automatic database and cache update
  async addAutoResponder(guildId, trigger, response, options = {}) {
    // Get current auto responders
    let autoResponders = await this.cache.getField(guildId, 'AutoResponder') || [];

    // Add new auto responder
    autoResponders.push({
      trigger,
      response,
      reply: options.reply || false,
      strict: options.strict !== undefined ? options.strict : true,
      delete: options.delete || false,
      selfDestruct: options.selfDestruct || 0
    });

    // Update in cache and database
    return await this.cache.setField(guildId, 'AutoResponder', autoResponders);
  }

  // Remove auto responder from a guild - automatic database and cache update
  async removeAutoResponder(guildId, trigger) {
    // Get current auto responders
    let autoResponders = await this.cache.getField(guildId, 'AutoResponder') || [];

    // Remove the auto responder
    autoResponders = autoResponders.filter(ar => ar.trigger !== trigger);

    // Update in cache and database
    return await this.cache.setField(guildId, 'AutoResponder', autoResponders);
  }

  // Clear all auto responders for a guild - automatic database and cache update
  async clearAllAutoResponders(guildId) {
    return await this.cache.setField(guildId, 'AutoResponder', []);
  }

  // Find auto responder by trigger - automatic cache handling
  async findAutoResponder(guildId, trigger) {
    const autoResponders = await this.getAutoResponders(guildId);
    return autoResponders.find(ar => ar.trigger === trigger);
  }

  // Get moderation cases - automatic cache handling
  async getModerationCases(guildId) {
    return await this.cache.getField(guildId, 'Moderation') || [];
  }

  // Get next case ID for a guild
  async getNextCaseId(guildId) {
    const cases = await this.getModerationCases(guildId);
    return cases.length > 0 ? Math.max(...cases.map(c => c.caseId)) + 1 : 1;
  }

  // Add moderation case - automatic database and cache update
  async addModerationCase(guildId, userId, moderatorId, action, reason, duration = null) {
    let cases = await this.getModerationCases(guildId);
    const caseId = await this.getNextCaseId(guildId);

    // Add new case
    cases.push({
      caseId,
      userId,
      moderatorId,
      action,
      reason,
      duration,
      timestamp: new Date()
    });

    return await this.cache.setField(guildId, 'Moderation', cases);
  }

  // Get moderation cases for a specific user
  async getUserModerationCases(guildId, userId) {
    const cases = await this.getModerationCases(guildId);
    return cases.filter(c => c.userId === userId);
  }

  // Get moderation case by case ID
  async getModerationCase(guildId, caseId) {
    const cases = await this.getModerationCases(guildId);
    return cases.find(c => c.caseId === caseId);
  }

  // ===== AUTOMOD FILTER METHODS =====

  // Get AutoMod filter configuration - automatic cache handling
  async getAutoModFilter(guildId) {
    const filter = await this.cache.getField(guildId, 'AutoModFilter');
    return filter || {
      enabled: false,
      ruleId: null,
      keywords: [],
      bypassRoles: [],
      action: 'delete',
      timeoutDuration: 300
    };
  }

  // Enable AutoMod filter - automatic database and cache update
  async enableAutoModFilter(guildId, ruleId = null) {
    const currentFilter = await this.getAutoModFilter(guildId);
    currentFilter.enabled = true;
    if (ruleId) currentFilter.ruleId = ruleId;
    return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
  }

  // Disable AutoMod filter - automatic database and cache update
  async disableAutoModFilter(guildId) {
    const currentFilter = await this.getAutoModFilter(guildId);
    currentFilter.enabled = false;
    currentFilter.ruleId = null;
    return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
  }

  // Add keyword to AutoMod filter - automatic database and cache update
  async addAutoModKeyword(guildId, keyword) {
    const currentFilter = await this.getAutoModFilter(guildId);
    const lowerKeyword = keyword.toLowerCase();

    if (!currentFilter.keywords.includes(lowerKeyword)) {
      currentFilter.keywords.push(lowerKeyword);
      return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
    }
    return false; // Keyword already exists
  }

  // Remove keyword from AutoMod filter - automatic database and cache update
  async removeAutoModKeyword(guildId, keyword) {
    const currentFilter = await this.getAutoModFilter(guildId);
    const lowerKeyword = keyword.toLowerCase();
    const index = currentFilter.keywords.indexOf(lowerKeyword);

    if (index > -1) {
      currentFilter.keywords.splice(index, 1);
      return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
    }
    return false; // Keyword not found
  }

  // Add bypass role to AutoMod filter - automatic database and cache update
  async addAutoModBypassRole(guildId, roleId) {
    const currentFilter = await this.getAutoModFilter(guildId);

    if (!currentFilter.bypassRoles.includes(roleId)) {
      currentFilter.bypassRoles.push(roleId);
      return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
    }
    return false; // Role already exists
  }

  // Remove bypass role from AutoMod filter - automatic database and cache update
  async removeAutoModBypassRole(guildId, roleId) {
    const currentFilter = await this.getAutoModFilter(guildId);
    const index = currentFilter.bypassRoles.indexOf(roleId);

    if (index > -1) {
      currentFilter.bypassRoles.splice(index, 1);
      return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
    }
    return false; // Role not found
  }

  // Set AutoMod rule ID - automatic database and cache update
  async setAutoModRuleId(guildId, ruleId) {
    const currentFilter = await this.getAutoModFilter(guildId);
    currentFilter.ruleId = ruleId;
    return await this.cache.setField(guildId, 'AutoModFilter', currentFilter);
  }

  // Reset AutoMod filter - automatic database and cache update
  async resetAutoModFilter(guildId) {
    const defaultFilter = {
      enabled: false,
      ruleId: null,
      keywords: [],
      bypassRoles: [],
      action: 'delete',
      timeoutDuration: 300
    };
    return await this.cache.setField(guildId, 'AutoModFilter', defaultFilter);
  }

  // ===== LAST.FM CROWN METHODS =====

  // Get Last.fm crowns for a guild - automatic cache handling
  async getLastFMCrowns(guildId) {
    return await this.cache.getField(guildId, 'LastFMCrowns') || [];
  }

  // Get crown for a specific artist - automatic cache handling
  async getLastFMCrown(guildId, artistName) {
    const crowns = await this.getLastFMCrowns(guildId);
    return crowns.find(crown => crown.artist.toLowerCase() === artistName.toLowerCase());
  }

  // Set/update crown for an artist - automatic database and cache update
  async setLastFMCrown(guildId, artistName, userId, plays) {
    let crowns = await this.getLastFMCrowns(guildId);

    // Remove existing crown for this artist
    crowns = crowns.filter(crown => crown.artist.toLowerCase() !== artistName.toLowerCase());

    // Add new crown
    crowns.push({
      artist: artistName,
      userId: userId,
      plays: plays,
      claimedAt: new Date()
    });

    return await this.cache.setField(guildId, 'LastFMCrowns', crowns);
  }

  // Remove crown for an artist - automatic database and cache update
  async removeLastFMCrown(guildId, artistName) {
    let crowns = await this.getLastFMCrowns(guildId);
    crowns = crowns.filter(crown => crown.artist.toLowerCase() !== artistName.toLowerCase());
    return await this.cache.setField(guildId, 'LastFMCrowns', crowns);
  }

  // Get all crowns for a specific user - automatic cache handling
  async getUserLastFMCrowns(guildId, userId) {
    const crowns = await this.getLastFMCrowns(guildId);
    return crowns.filter(crown => crown.userId === userId);
  }

  // Remove all crowns for a specific user - automatic database and cache update
  async removeUserLastFMCrowns(guildId, userId) {
    let crowns = await this.getLastFMCrowns(guildId);
    crowns = crowns.filter(crown => crown.userId !== userId);
    return await this.cache.setField(guildId, 'LastFMCrowns', crowns);
  }

  // Clear all Last.fm crowns for a guild - automatic database and cache update
  async clearAllLastFMCrowns(guildId) {
    return await this.cache.setField(guildId, 'LastFMCrowns', []);
  }

  // ===== SWEAR TRACKER METHODS =====

  // Get swear tracker data for a guild - automatic cache handling
  async getSwearTracker(guildId) {
    return await this.cache.getField(guildId, 'SwearTracker') || [];
  }

  // Get swear data for a specific user in a guild
  async getUserSwearData(guildId, userId) {
    const swearTracker = await this.getSwearTracker(guildId);
    return swearTracker.find(entry => entry.userId === userId) || null;
  }

  // Add or update swear count for a user
  async addSwear(guildId, userId, swearWord) {
    let swearTracker = await this.getSwearTracker(guildId);
    let userEntry = swearTracker.find(entry => entry.userId === userId);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (!userEntry) {
      // Create new user entry
      userEntry = {
        userId: userId,
        swears: [],
        totalSwears: 0,
        todaySwears: 0,
        lastSwearDate: new Date()
      };
      swearTracker.push(userEntry);
    }

    // Reset today's count if it's a new day
    const lastSwearDate = new Date(userEntry.lastSwearDate);
    lastSwearDate.setHours(0, 0, 0, 0);
    if (lastSwearDate.getTime() !== today.getTime()) {
      userEntry.todaySwears = 0;
    }

    // Find or create swear word entry
    let swearEntry = userEntry.swears.find(s => s.word === swearWord);
    if (!swearEntry) {
      swearEntry = {
        word: swearWord,
        count: 0,
        lastUsed: new Date()
      };
      userEntry.swears.push(swearEntry);
    }

    // Update counts
    swearEntry.count++;
    swearEntry.lastUsed = new Date();
    userEntry.totalSwears++;
    userEntry.todaySwears++;
    userEntry.lastSwearDate = new Date();

    // Update in cache and database
    return await this.cache.setField(guildId, 'SwearTracker', swearTracker);
  }

  // Get swear leaderboard for a guild
  async getSwearLeaderboard(guildId) {
    const swearTracker = await this.getSwearTracker(guildId);
    return swearTracker
      .map(entry => {
        // Calculate actual total from individual word counts
        const calculatedTotal = entry.swears ? entry.swears.reduce((total, swear) => total + swear.count, 0) : 0;
        return { ...entry, calculatedTotal };
      })
      .filter(entry => entry.calculatedTotal > 0)
      .sort((a, b) => b.calculatedTotal - a.calculatedTotal);
  }

  // Get user's position in swear leaderboard
  async getUserSwearPosition(guildId, userId) {
    const leaderboard = await this.getSwearLeaderboard(guildId);
    const position = leaderboard.findIndex(entry => entry.userId === userId);
    return position === -1 ? null : position + 1;
  }

  // Clear all swear data for a guild - automatic database and cache update
  async clearAllSwearData(guildId) {
    return await this.cache.setField(guildId, 'SwearTracker', []);
  }

  // ===== GUILD NAME HISTORY METHODS =====

  // Get guild name history - automatic cache handling
  async getGuildNameHistory(guildId) {
    return await this.cache.getField(guildId, 'GuildNameHistory') || [];
  }

  // Add a guild name change entry
  async addGuildNameChange(guildId, oldName, newName, changedBy = null) {
    let nameHistory = await this.getGuildNameHistory(guildId);

    const nameChange = {
      oldName: oldName,
      newName: newName,
      changedBy: changedBy,
      changedAt: new Date()
    };

    nameHistory.push(nameChange);

    // Keep only the last 50 entries to prevent unlimited growth
    if (nameHistory.length > 50) {
      nameHistory.splice(0, nameHistory.length - 50);
    }

    return await this.cache.setField(guildId, 'GuildNameHistory', nameHistory);
  }

  // Clear all guild name history - automatic database and cache update
  async clearAllGuildNameHistory(guildId) {
    return await this.cache.setField(guildId, 'GuildNameHistory', []);
  }

  // ===== BIRTHDAY ROLE METHODS =====

  // Get birthday role - automatic cache handling
  async getBirthdayRole(guildId) {
    return await this.cache.getField(guildId, 'BirthdayRole');
  }

  // Set birthday role - automatic database and cache update
  async setBirthdayRole(guildId, roleId) {
    return await this.cache.setField(guildId, 'BirthdayRole', roleId);
  }

  // Remove birthday role - automatic database and cache update
  async removeBirthdayRole(guildId) {
    return await this.cache.setField(guildId, 'BirthdayRole', null);
  }

  // ===== MESSAGE STATISTICS METHODS =====

  // Get message statistics for a guild - automatic cache handling
  async getMessageStats(guildId, userId = null) {
    const messageStats = await this.cache.getField(guildId, 'MessageStats') || [];

    if (userId) {
      // Return stats for specific user
      return messageStats.filter(stat => stat.userId === userId);
    }

    return messageStats;
  }

  // Track a message for a user
  async trackMessage(guildId, userId) {
    let messageStats = await this.getMessageStats(guildId);

    // Get today's date (without time)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find existing entry for user and today
    const existingEntry = messageStats.find(stat =>
      stat.userId === userId &&
      new Date(stat.date).getTime() === today.getTime()
    );

    if (existingEntry) {
      // Increment count for today
      existingEntry.count += 1;
    } else {
      // Create new entry for today
      messageStats.push({
        userId: userId,
        date: today,
        count: 1
      });
    }

    // Keep only last 90 days of data to prevent unlimited growth
    const ninetyDaysAgo = new Date(today.getTime() - (90 * 24 * 60 * 60 * 1000));
    messageStats = messageStats.filter(stat => new Date(stat.date) >= ninetyDaysAgo);

    return await this.cache.setField(guildId, 'MessageStats', messageStats);
  }

  // Get total message count for a user
  async getTotalMessageCount(guildId, userId) {
    const userStats = await this.getMessageStats(guildId, userId);
    return userStats.reduce((total, stat) => total + stat.count, 0);
  }

  // Utility methods
  invalidateGuild(guildId) {
    this.cache.invalidate(guildId);
  }

  clearCache() {
    this.cache.clear();
  }

  getStats() {
    return {
      guilds: this.cache.getStats()
    };
  }
}

// Export singleton instance
module.exports = new Guild();
