const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  UserID: {
    type: String,
    required: true,
    unique: true
  },
  SelfPrefix: {
    type: String,
    default: null
  },
  Timezone: {
    type: String,
    default: null
  },
  LastFMUsername: {
    type: String,
    default: null
  },
  // Last.fm library data
  LastFMLibrary: {
    artists: [{
      name: {
        type: String,
        required: true
      },
      plays: {
        type: Number,
        required: true,
        default: 0
      }
    }],
    albums: [{
      name: {
        type: String,
        required: true
      },
      artist: {
        type: String,
        required: true
      },
      plays: {
        type: Number,
        required: true,
        default: 0
      }
    }],
    tracks: [{
      name: {
        type: String,
        required: true
      },
      artist: {
        type: String,
        required: true
      },
      plays: {
        type: Number,
        required: true,
        default: 0
      }
    }],
    lastUpdated: {
      type: Date,
      default: null
    }
  },
  // Last.fm custom settings
  LastFMSettings: {
    customMode: {
      type: String,
      default: null
    },
    reactions: {
      upvote: {
        type: String,
        default: null
      },
      downvote: {
        type: String,
        default: null
      }
    }
  },
  TicTacToe: {
    Wins: {
      type: Number,
      default: 0
    },
    Losses: {
      type: Number,
      default: 0
    },
    Matches: {
      type: Number,
      default: 0
    }
  },
  // Gambling system
  Gambling: {
    wallet: {
      type: Number,
      default: 1000 // Starting wallet balance
    },
    bank: {
      type: Number,
      default: 0 // Starting bank balance
    },
    dailyClaim: {
      lastClaimed: {
        type: Date,
        default: null
      },
      streak: {
        type: Number,
        default: 0
      }
    },
    robCooldown: {
      type: Date,
      default: null
    },
    lastWorked: {
      type: Date,
      default: null
    },
    begCooldown: {
      type: Date,
      default: null
    },
    lastInterestClaim: {
      type: Date,
      default: null
    },
    totalGambled: {
      type: Number,
      default: 0
    },
    totalWon: {
      type: Number,
      default: 0
    },
    totalLost: {
      type: Number,
      default: 0
    }
  },
  // Roleplay interaction tracking
  Roleplay: {
    // Track interactions given by this user to others
    given: [{
      targetUserId: {
        type: String,
        required: true
      },
      action: {
        type: String,
        required: true
      },
      count: {
        type: Number,
        default: 1
      }
    }],
    // Track interactions received by this user from others
    received: [{
      fromUserId: {
        type: String,
        required: true
      },
      action: {
        type: String,
        required: true
      },
      count: {
        type: Number,
        default: 1
      }
    }]
  },
  // Personal notes system
  Notes: [{
    id: {
      type: Number,
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: 500 // Limit note length
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Name history tracking
  NameHistory: [{
    type: {
      type: String,
      required: true,
      enum: ['username', 'displayname', 'nickname'] // Track different types of name changes
    },
    oldName: {
      type: String,
      required: true
    },
    newName: {
      type: String,
      required: true
    },
    guildId: {
      type: String,
      default: null // null for global changes (username/displayname), guildId for nickname changes
    },
    changedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Birthday system
  Birthday: {
    day: {
      type: Number,
      default: null,
      min: 1,
      max: 31
    },
    month: {
      type: Number,
      default: null,
      min: 1,
      max: 12
    },
    setAt: {
      type: Date,
      default: null
    }
  },

  // Spotify integration
  Spotify: {
    accessToken: {
      type: String,
      default: null
    },
    refreshToken: {
      type: String,
      default: null
    },
    tokenExpiry: {
      type: Date,
      default: null
    },
    spotifyId: {
      type: String,
      default: null
    },
    displayName: {
      type: String,
      default: null
    },
    email: {
      type: String,
      default: null
    },
    country: {
      type: String,
      default: null
    },
    product: {
      type: String,
      default: null // free, premium
    },
    connectedAt: {
      type: Date,
      default: null
    }
  }
}, {
  timestamps: true
});

// Create indexes for better performance
userSchema.index({ UserID: 1 });
userSchema.index({ LastFMUsername: 1 });
userSchema.index({ 'LastFMLibrary.artists.name': 1 });
userSchema.index({ 'LastFMLibrary.albums.name': 1, 'LastFMLibrary.albums.artist': 1 });
userSchema.index({ 'LastFMLibrary.tracks.name': 1, 'LastFMLibrary.tracks.artist': 1 });
userSchema.index({ 'Spotify.spotifyId': 1 });

module.exports = mongoose.model('User', userSchema);
